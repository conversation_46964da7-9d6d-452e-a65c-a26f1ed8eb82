// Package declaration
package main

// Import packages
import (
	"fmt"
	"math/rand"
	"time"
)

// WEB SCRAPER SIMULATION
// Demonstrates practical use of goroutines

// Struct to represent a web page
type WebPage struct {
	URL     string
	Title   string
	Size    int // Size in KB
	LoadTime time.Duration
}

// Struct to represent scraping result
type ScrapeResult struct {
	Page    WebPage
	Success bool
	Error   string
}

// Function to simulate scraping a single web page
func scrapePage(url string) ScrapeResult {
	fmt.Printf("🌐 Starting to scrape: %s\n", url)
	
	// Simulate random load time (100ms to 800ms)
	loadTime := time.Duration(100+rand.Intn(700)) * time.Millisecond
	time.Sleep(loadTime)
	
	// Simulate occasional failures (10% chance)
	if rand.Float32() < 0.1 {
		return ScrapeResult{
			Page:    WebPage{URL: url},
			Success: false,
			Error:   "Connection timeout",
		}
	}
	
	// Simulate successful scraping
	page := WebPage{
		URL:      url,
		Title:    fmt.Sprintf("Page Title for %s", url),
		Size:     50 + rand.Intn(200), // Random size 50-250 KB
		LoadTime: loadTime,
	}
	
	fmt.Printf("✅ Successfully scraped: %s (took %v)\n", url, loadTime)
	
	return ScrapeResult{
		Page:    page,
		Success: true,
		Error:   "",
	}
}

// Function to simulate processing scraped data
func processPageData(page WebPage) {
	fmt.Printf("🔄 Processing data from: %s\n", page.URL)
	
	// Simulate processing time based on page size
	processingTime := time.Duration(page.Size) * time.Millisecond
	time.Sleep(processingTime)
	
	fmt.Printf("✅ Processed data from: %s (size: %d KB)\n", page.URL, page.Size)
}

// Function to simulate saving data to database
func saveToDatabase(page WebPage) {
	fmt.Printf("💾 Saving to database: %s\n", page.URL)
	
	// Simulate database save time
	time.Sleep(50 * time.Millisecond)
	
	fmt.Printf("✅ Saved to database: %s\n", page.URL)
}

// Function to scrape multiple pages sequentially
func scrapeSequentially(urls []string) time.Duration {
	fmt.Println("=== Sequential Scraping ===")
	start := time.Now()
	
	var results []ScrapeResult
	
	for _, url := range urls {
		result := scrapePage(url)
		results = append(results, result)
		
		if result.Success {
			processPageData(result.Page)
			saveToDatabase(result.Page)
		}
	}
	
	duration := time.Since(start)
	
	// Print summary
	successful := 0
	for _, result := range results {
		if result.Success {
			successful++
		}
	}
	
	fmt.Printf("Sequential scraping completed: %d/%d successful in %v\n", 
		successful, len(urls), duration)
	
	return duration
}

// Function to scrape multiple pages concurrently
func scrapeConcurrently(urls []string) time.Duration {
	fmt.Println("\n=== Concurrent Scraping ===")
	start := time.Now()
	
	// Channel to collect results
	results := make(chan ScrapeResult, len(urls))
	
	// Start goroutines for each URL
	for _, url := range urls {
		go func(u string) {
			result := scrapePage(u)
			results <- result // Send result to channel
		}(url)
	}
	
	// Collect results
	var scrapeResults []ScrapeResult
	for i := 0; i < len(urls); i++ {
		result := <-results // Receive result from channel
		scrapeResults = append(scrapeResults, result)
	}
	
	// Process successful results concurrently
	processChannel := make(chan WebPage, len(scrapeResults))
	
	for _, result := range scrapeResults {
		if result.Success {
			go func(page WebPage) {
				processPageData(page)
				processChannel <- page
			}(result.Page)
		}
	}
	
	// Wait for processing and save to database
	successfulCount := 0
	for _, result := range scrapeResults {
		if result.Success {
			page := <-processChannel
			go saveToDatabase(page)
			successfulCount++
		}
	}
	
	// Wait for database saves to complete
	time.Sleep(100 * time.Millisecond)
	
	duration := time.Since(start)
	
	fmt.Printf("Concurrent scraping completed: %d/%d successful in %v\n", 
		successfulCount, len(urls), duration)
	
	return duration
}

// Function to demonstrate worker pool pattern
func scrapeWithWorkerPool(urls []string, numWorkers int) {
	fmt.Printf("\n=== Worker Pool Scraping (%d workers) ===\n", numWorkers)
	start := time.Now()
	
	// Create channels
	jobs := make(chan string, len(urls))
	results := make(chan ScrapeResult, len(urls))
	
	// Start workers
	for w := 1; w <= numWorkers; w++ {
		go func(workerID int) {
			fmt.Printf("👷 Worker %d started\n", workerID)
			
			for url := range jobs {
				fmt.Printf("👷 Worker %d scraping: %s\n", workerID, url)
				result := scrapePage(url)
				results <- result
			}
			
			fmt.Printf("👷 Worker %d finished\n", workerID)
		}(w)
	}
	
	// Send jobs
	for _, url := range urls {
		jobs <- url
	}
	close(jobs) // Close jobs channel to signal no more work
	
	// Collect results
	successful := 0
	for i := 0; i < len(urls); i++ {
		result := <-results
		if result.Success {
			successful++
		}
	}
	
	duration := time.Since(start)
	
	fmt.Printf("Worker pool scraping completed: %d/%d successful in %v\n", 
		successful, len(urls), duration)
}

// Main function
func main() {
	fmt.Println("=== WEB SCRAPER WITH GOROUTINES ===")
	fmt.Println("Demonstrating concurrent web scraping simulation")
	fmt.Println()
	
	// Seed random number generator
	rand.Seed(time.Now().UnixNano())
	
	// List of URLs to scrape
	urls := []string{
		"https://example.com/page1",
		"https://example.com/page2",
		"https://example.com/page3",
		"https://example.com/page4",
		"https://example.com/page5",
		"https://example.com/page6",
		"https://example.com/page7",
		"https://example.com/page8",
	}
	
	fmt.Printf("Scraping %d URLs...\n\n", len(urls))
	
	// SEQUENTIAL SCRAPING
	sequentialTime := scrapeSequentially(urls)
	
	// Wait a bit between tests
	time.Sleep(500 * time.Millisecond)
	
	// CONCURRENT SCRAPING
	concurrentTime := scrapeConcurrently(urls)
	
	// Wait a bit between tests
	time.Sleep(500 * time.Millisecond)
	
	// WORKER POOL SCRAPING
	scrapeWithWorkerPool(urls, 3)
	
	// PERFORMANCE COMPARISON
	fmt.Println("\n=== Performance Comparison ===")
	fmt.Printf("Sequential time: %v\n", sequentialTime)
	fmt.Printf("Concurrent time: %v\n", concurrentTime)
	
	if sequentialTime > concurrentTime {
		speedup := float64(sequentialTime) / float64(concurrentTime)
		fmt.Printf("Concurrent scraping was %.2fx faster!\n", speedup)
	}
	
	// REAL-WORLD CONSIDERATIONS
	fmt.Println("\n=== Real-World Considerations ===")
	fmt.Println("1. Rate limiting: Don't overwhelm servers")
	fmt.Println("2. Respect robots.txt and terms of service")
	fmt.Println("3. Handle errors gracefully")
	fmt.Println("4. Use connection pooling for efficiency")
	fmt.Println("5. Implement retry logic for failed requests")
	fmt.Println("6. Monitor resource usage (memory, CPU)")
	
	// DEMONSTRATE RATE LIMITING
	fmt.Println("\n=== Rate Limited Scraping ===")
	fmt.Println("Scraping with delays to be respectful...")
	
	rateLimitedScrape := func(urls []string, delay time.Duration) {
		for i, url := range urls[:3] { // Just first 3 URLs
			if i > 0 {
				time.Sleep(delay) // Wait between requests
			}
			
			go func(u string) {
				result := scrapePage(u)
				if result.Success {
					fmt.Printf("📊 Rate-limited scrape successful: %s\n", u)
				}
			}(url)
		}
		
		time.Sleep(1 * time.Second) // Wait for goroutines to complete
	}
	
	rateLimitedScrape(urls, 200*time.Millisecond)
	
	fmt.Println()
	fmt.Println("Web scraper demonstration complete! 🕷️")
	fmt.Println("Key takeaway: Goroutines make concurrent operations simple and efficient!")
}
