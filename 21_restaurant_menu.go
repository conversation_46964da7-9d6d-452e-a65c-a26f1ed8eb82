// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// RESTAURANT ORDERING SYSTEM
	// Demonstrates real-world use of if-else and switch statements
	
	// Customer information
	customerAge := 16
	membershipLevel := "Gold"
	orderAmount := 45.50
	
	// Menu item selection
	var menuChoice int
	
	// Display menu
	fmt.Println("=== WELCOME TO TASTY BITES RESTAURANT ===")
	fmt.Println("1. Burger - $12.99")
	fmt.Println("2. Pizza - $18.50")
	fmt.Println("3. Salad - $9.99")
	fmt.Println("4. Pasta - $14.75")
	fmt.Println("5. Steak - $28.99")
	fmt.Println()
	
	// Simulate customer choice
	menuChoice = 2 // Customer chose pizza
	
	// Process menu selection using switch
	var itemName string
	var itemPrice float64
	
	switch menuChoice {
	case 1:
		itemName = "Burger"
		itemPrice = 12.99
	case 2:
		itemName = "Pizza"
		itemPrice = 18.50
	case 3:
		itemName = "Salad"
		itemPrice = 9.99
	case 4:
		itemName = "Pasta"
		itemPrice = 14.75
	case 5:
		itemName = "Steak"
		itemPrice = 28.99
	default:
		fmt.Println("Invalid menu choice!")
		return // Exit the program
	}
	
	fmt.Printf("You selected: %s - $%.2f\n", itemName, itemPrice)
	fmt.Println()
	
	// Age verification for certain items
	if itemName == "Steak" {
		// Check if customer is old enough for premium items
		if customerAge < 21 {
			fmt.Println("Note: Steak comes with complimentary wine for customers 21+")
			fmt.Println("You'll receive a soft drink instead.")
		} else {
			fmt.Println("Steak includes complimentary wine!")
		}
	}
	
	// Calculate discount based on membership level
	var discountPercent float64
	
	switch membershipLevel {
	case "Bronze":
		discountPercent = 5.0
	case "Silver":
		discountPercent = 10.0
	case "Gold":
		discountPercent = 15.0
	case "Platinum":
		discountPercent = 20.0
	default:
		discountPercent = 0.0
	}
	
	// Apply discount if applicable
	var finalPrice float64
	if discountPercent > 0 {
		discountAmount := itemPrice * (discountPercent / 100)
		finalPrice = itemPrice - discountAmount
		
		fmt.Printf("Membership Level: %s\n", membershipLevel)
		fmt.Printf("Discount: %.0f%% (-$%.2f)\n", discountPercent, discountAmount)
	} else {
		finalPrice = itemPrice
		fmt.Println("No membership discount applied")
	}
	
	// Check for free delivery
	if orderAmount >= 30.0 {
		fmt.Println("🚚 Free delivery included!")
	} else {
		fmt.Printf("Delivery fee: $3.99 (Free delivery on orders $30+)\n")
		finalPrice += 3.99
	}
	
	// Display final order summary
	fmt.Println()
	fmt.Println("=== ORDER SUMMARY ===")
	fmt.Printf("Item: %s\n", itemName)
	fmt.Printf("Customer Age: %d\n", customerAge)
	fmt.Printf("Membership: %s\n", membershipLevel)
	fmt.Printf("Final Price: $%.2f\n", finalPrice)
	
	// Payment method recommendation
	if finalPrice > 50.0 {
		fmt.Println("💳 Recommended: Pay with credit card for rewards points")
	} else {
		fmt.Println("💵 Cash or card accepted")
	}
	
	fmt.Println()
	fmt.Println("Thank you for dining with us! 🍽️")
}
