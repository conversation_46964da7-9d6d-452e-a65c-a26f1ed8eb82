// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// FOR LOOPS - Repeat code multiple times
	
	// BASIC FOR LOOP (like traditional for loops)
	// for initialization; condition; increment
	fmt.Println("=== Basic For Loop ===")
	for i := 1; i <= 5; i++ {
		// i starts at 1, continues while i <= 5, increases by 1 each time
		fmt.Printf("Count: %d\n", i)
	}
	
	fmt.Println()
	
	// COUNTDOWN LOOP
	fmt.Println("=== Countdown ===")
	for i := 10; i >= 1; i-- {
		// i starts at 10, continues while i >= 1, decreases by 1 each time
		fmt.Printf("T-minus %d\n", i)
	}
	fmt.Println("Blast off! 🚀")
	
	fmt.Println()
	
	// WHILE-STYLE LOOP (only condition)
	fmt.Println("=== While-style Loop ===")
	number := 1
	for number <= 3 {
		// Keep looping as long as condition is true
		fmt.Printf("Number is %d\n", number)
		number++ // Don't forget to increment, or it loops forever!
	}
	
	fmt.Println()
	
	// INFINITE LOOP with break
	fmt.Println("=== Infinite Loop with Break ===")
	counter := 0
	for {
		// for with no condition runs forever
		counter++
		fmt.Printf("Counter: %d\n", counter)
		
		// Use break to exit the loop
		if counter >= 3 {
			fmt.Println("Breaking out of loop!")
			break
		}
	}
	
	fmt.Println()
	
	// NESTED LOOPS (loop inside another loop)
	fmt.Println("=== Nested Loops - Multiplication Table ===")
	for i := 1; i <= 3; i++ {
		// Outer loop runs 3 times
		for j := 1; j <= 3; j++ {
			// Inner loop runs 3 times for each outer loop iteration
			result := i * j
			fmt.Printf("%d x %d = %d\t", i, j, result) // \t adds a tab
		}
		fmt.Println() // New line after each row
	}
	
	fmt.Println()
	
	// LOOP WITH DIFFERENT INCREMENTS
	fmt.Println("=== Even Numbers ===")
	for i := 2; i <= 10; i += 2 {
		// i increases by 2 each time (i += 2 is same as i = i + 2)
		fmt.Printf("Even number: %d\n", i)
	}
}
