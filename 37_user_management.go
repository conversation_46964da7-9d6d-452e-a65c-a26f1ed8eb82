// Package declaration
package main

// Import fmt for input/output
import "fmt"

// USER MANAGEMENT SYSTEM
// Demonstrates practical use of multiple return values

// User struct to represent user data
type User struct {
	ID       int
	Username string
	Email    string
	Age      int
	Active   bool
}

// Global user database (in real app, this would be a database)
var users = []User{
	{1, "alice123", "<EMAIL>", 28, true},
	{2, "bob_dev", "<EMAIL>", 32, true},
	{3, "charlie", "<EMAIL>", 25, false},
	{4, "diana_photo", "<EMAIL>", 30, true},
}

// Function to find user by username
// Returns user data and whether user was found
func findUserByUsername(username string) (User, bool) {
	for _, user := range users {
		if user.Username == username {
			return user, true // Found user
		}
	}
	return User{}, false // User not found, return empty User
}

// Function to find user by ID
// Returns user data and whether user was found
func findUserByID(id int) (User, bool) {
	for _, user := range users {
		if user.ID == id {
			return user, true
		}
	}
	return User{}, false
}

// Function to validate user registration data
// Returns success status and error message
func validateRegistration(username, email string, age int) (bool, string) {
	// Check username length
	if len(username) < 3 {
		return false, "Username must be at least 3 characters"
	}
	
	if len(username) > 20 {
		return false, "Username must be less than 20 characters"
	}
	
	// Check if username already exists
	_, exists := findUserByUsername(username)
	if exists {
		return false, "Username already taken"
	}
	
	// Check email format (simple check)
	if len(email) < 5 || !contains(email, "@") || !contains(email, ".") {
		return false, "Invalid email format"
	}
	
	// Check age
	if age < 13 {
		return false, "Must be at least 13 years old"
	}
	
	if age > 120 {
		return false, "Age seems unrealistic"
	}
	
	return true, "Registration data is valid"
}

// Helper function to check if string contains substring
func contains(str, substr string) bool {
	for i := 0; i <= len(str)-len(substr); i++ {
		if str[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// Function to authenticate user
// Returns success status and user data
func authenticateUser(username, password string) (bool, User, string) {
	// Simple password check (in real app, use proper hashing)
	passwords := map[string]string{
		"alice123":    "password123",
		"bob_dev":     "securepass",
		"charlie":     "mypassword",
		"diana_photo": "photopass",
	}
	
	// Check if user exists
	user, userExists := findUserByUsername(username)
	if !userExists {
		return false, User{}, "User not found"
	}
	
	// Check if account is active
	if !user.Active {
		return false, User{}, "Account is deactivated"
	}
	
	// Check password
	if storedPassword, exists := passwords[username]; exists {
		if storedPassword == password {
			return true, user, "Login successful"
		}
	}
	
	return false, User{}, "Invalid password"
}

// Function to get user statistics
// Returns multiple statistics about users
func getUserStatistics() (int, int, int, float64) {
	totalUsers := len(users)
	activeUsers := 0
	totalAge := 0
	
	for _, user := range users {
		if user.Active {
			activeUsers++
		}
		totalAge += user.Age
	}
	
	averageAge := float64(totalAge) / float64(totalUsers)
	inactiveUsers := totalUsers - activeUsers
	
	return totalUsers, activeUsers, inactiveUsers, averageAge
}

// Function to search users by age range
// Returns matching users and count
func searchUsersByAge(minAge, maxAge int) ([]User, int) {
	var matchingUsers []User
	
	for _, user := range users {
		if user.Age >= minAge && user.Age <= maxAge {
			matchingUsers = append(matchingUsers, user)
		}
	}
	
	return matchingUsers, len(matchingUsers)
}

// Main function
func main() {
	fmt.Println("=== USER MANAGEMENT SYSTEM ===")
	fmt.Println("Demonstrating multiple return values in practice")
	fmt.Println()
	
	// USER LOOKUP
	fmt.Println("=== User Lookup ===")
	
	// Search by username
	searchUsernames := []string{"alice123", "bob_dev", "nonexistent"}
	
	for _, username := range searchUsernames {
		user, found := findUserByUsername(username)
		
		if found {
			fmt.Printf("✅ Found user: %s (ID: %d, Email: %s, Age: %d, Active: %t)\n",
				user.Username, user.ID, user.Email, user.Age, user.Active)
		} else {
			fmt.Printf("❌ User '%s' not found\n", username)
		}
	}
	
	fmt.Println()
	
	// Search by ID
	fmt.Println("=== User Lookup by ID ===")
	searchIDs := []int{1, 3, 999}
	
	for _, id := range searchIDs {
		user, found := findUserByID(id)
		
		if found {
			fmt.Printf("✅ Found user ID %d: %s (%s)\n", id, user.Username, user.Email)
		} else {
			fmt.Printf("❌ User ID %d not found\n", id)
		}
	}
	
	fmt.Println()
	
	// REGISTRATION VALIDATION
	fmt.Println("=== Registration Validation ===")
	
	// Test registration data
	registrations := []struct {
		username string
		email    string
		age      int
	}{
		{"newuser", "<EMAIL>", 25},
		{"xy", "invalid", 20},                    // Username too short, invalid email
		{"alice123", "<EMAIL>", 30},     // Username taken
		{"validuser", "<EMAIL>", 12},     // Too young
		{"gooduser", "<EMAIL>", 28},       // Valid
	}
	
	for _, reg := range registrations {
		isValid, message := validateRegistration(reg.username, reg.email, reg.age)
		
		fmt.Printf("Registration: %s, %s, age %d\n", reg.username, reg.email, reg.age)
		if isValid {
			fmt.Printf("  ✅ %s\n", message)
		} else {
			fmt.Printf("  ❌ %s\n", message)
		}
		fmt.Println()
	}
	
	// USER AUTHENTICATION
	fmt.Println("=== User Authentication ===")
	
	// Test login attempts
	loginAttempts := []struct {
		username string
		password string
	}{
		{"alice123", "password123"},    // Valid
		{"bob_dev", "wrongpass"},       // Wrong password
		{"charlie", "mypassword"},      // Inactive account
		{"nonexistent", "anypass"},     // User doesn't exist
		{"diana_photo", "photopass"},   // Valid
	}
	
	for _, attempt := range loginAttempts {
		success, user, message := authenticateUser(attempt.username, attempt.password)
		
		fmt.Printf("Login attempt: %s / %s\n", attempt.username, attempt.password)
		if success {
			fmt.Printf("  ✅ %s - Welcome %s!\n", message, user.Username)
		} else {
			fmt.Printf("  ❌ %s\n", message)
		}
		fmt.Println()
	}
	
	// USER STATISTICS
	fmt.Println("=== User Statistics ===")
	
	total, active, inactive, avgAge := getUserStatistics()
	
	fmt.Printf("Total Users: %d\n", total)
	fmt.Printf("Active Users: %d (%.1f%%)\n", active, float64(active)/float64(total)*100)
	fmt.Printf("Inactive Users: %d (%.1f%%)\n", inactive, float64(inactive)/float64(total)*100)
	fmt.Printf("Average Age: %.1f years\n", avgAge)
	
	fmt.Println()
	
	// AGE-BASED SEARCH
	fmt.Println("=== Age-Based Search ===")
	
	// Search for users in different age ranges
	ageRanges := [][2]int{
		{20, 25},
		{26, 30},
		{31, 35},
		{18, 40},
	}
	
	for _, ageRange := range ageRanges {
		minAge, maxAge := ageRange[0], ageRange[1]
		matchingUsers, count := searchUsersByAge(minAge, maxAge)
		
		fmt.Printf("Users aged %d-%d: %d found\n", minAge, maxAge, count)
		
		for _, user := range matchingUsers {
			status := "Active"
			if !user.Active {
				status = "Inactive"
			}
			fmt.Printf("  - %s (age %d, %s)\n", user.Username, user.Age, status)
		}
		fmt.Println()
	}
	
	fmt.Println("User management system demonstration complete! 👥")
}
