// Package declaration
package main

// Import fmt for printing
import "fmt"

// Main function
func main() {
	// Method 1: Declare a variable and then assign a value
	// var keyword tells Go we're creating a variable
	// string is the type of data (text)
	var name string
	name = "Alice"
	
	// Method 2: Declare and assign in one line
	// Go automatically figures out the type
	var age = 25
	
	// Method 3: Short declaration (most common way)
	// := means "declare and assign"
	city := "New York"
	
	// Print the variables
	// We can use variables inside fmt.Println
	fmt.Println("Name:", name)
	fmt.Println("Age:", age)
	fmt.Println("City:", city)
	
	// Variables can be changed (that's why they're called "variable")
	age = 26
	fmt.Println("New age:", age)
	
	// You can declare multiple variables at once
	var firstName, lastName string = "John", "Doe"
	fmt.Println("Full name:", firstName, lastName)
}
