// Package declaration
package main

// Import fmt for input/output
import "fmt"

// INTERFACE DEFINITIONS
// Interfaces define method signatures that types must implement

// Shape interface - any type with Area() method is a Shape
type Shape interface {
	Area() float64
}

// Drawable interface - any type with Draw() method is Drawable
type Drawable interface {
	Draw()
}

// Combined interface - any type that implements both Shape and Drawable
type DrawableShape interface {
	Shape    // Embed Shape interface
	Drawable // Embed Drawable interface
}

// Vehicle interface - defines what methods a vehicle must have
type Vehicle interface {
	Start() string
	Stop() string
	GetSpeed() int
}

// STRUCT DEFINITIONS

// Rectangle struct
type Rectangle struct {
	Width  float64
	Height float64
}

// Circle struct
type Circle struct {
	Radius float64
}

// Car struct
type Car struct {
	Brand string
	Speed int
}

// Motorcycle struct
type Motorcycle struct {
	Brand string
	Speed int
}

// IMPLEMENTING INTERFACES
// In Go, you implement an interface by having the required methods
// No explicit "implements" keyword needed

// Rectangle implements Shape interface
func (r Rectangle) Area() float64 {
	return r.Width * r.Height
}

// Rectangle implements Drawable interface
func (r Rectangle) Draw() {
	fmt.Printf("Drawing a rectangle: %.1f x %.1f\n", r.Width, r.Height)
}

// Circle implements Shape interface
func (c Circle) Area() float64 {
	const pi = 3.14159
	return pi * c.Radius * c.Radius
}

// Circle implements Drawable interface
func (c Circle) Draw() {
	fmt.Printf("Drawing a circle with radius %.1f\n", c.Radius)
}

// Car implements Vehicle interface
func (c Car) Start() string {
	return fmt.Sprintf("%s car is starting...", c.Brand)
}

func (c Car) Stop() string {
	return fmt.Sprintf("%s car is stopping...", c.Brand)
}

func (c Car) GetSpeed() int {
	return c.Speed
}

// Motorcycle implements Vehicle interface
func (m Motorcycle) Start() string {
	return fmt.Sprintf("%s motorcycle is revving up...", m.Brand)
}

func (m Motorcycle) Stop() string {
	return fmt.Sprintf("%s motorcycle is braking...", m.Brand)
}

func (m Motorcycle) GetSpeed() int {
	return m.Speed
}

// FUNCTIONS THAT USE INTERFACES

// Function that works with any Shape
func printArea(s Shape) {
	fmt.Printf("Area: %.2f\n", s.Area())
}

// Function that works with any Drawable
func drawShape(d Drawable) {
	d.Draw()
}

// Function that works with any DrawableShape
func processDrawableShape(ds DrawableShape) {
	ds.Draw()
	fmt.Printf("Area: %.2f\n", ds.Area())
}

// Function that works with any Vehicle
func operateVehicle(v Vehicle) {
	fmt.Println(v.Start())
	fmt.Printf("Current speed: %d mph\n", v.GetSpeed())
	fmt.Println(v.Stop())
}

// Main function
func main() {
	fmt.Println("=== INTERFACES IN GO ===")
	fmt.Println()
	
	// CREATE INSTANCES
	rect := Rectangle{Width: 5.0, Height: 3.0}
	circle := Circle{Radius: 2.5}
	car := Car{Brand: "Toyota", Speed: 60}
	motorcycle := Motorcycle{Brand: "Harley", Speed: 80}
	
	// USING INTERFACES
	fmt.Println("=== Using Shape Interface ===")
	
	// Both Rectangle and Circle implement Shape interface
	// So they can be used wherever Shape is expected
	printArea(rect)   // Rectangle as Shape
	printArea(circle) // Circle as Shape
	
	fmt.Println()
	
	// USING DRAWABLE INTERFACE
	fmt.Println("=== Using Drawable Interface ===")
	
	drawShape(rect)   // Rectangle as Drawable
	drawShape(circle) // Circle as Drawable
	
	fmt.Println()
	
	// USING COMBINED INTERFACE
	fmt.Println("=== Using Combined Interface ===")
	
	// Both Rectangle and Circle implement DrawableShape
	// because they implement both Shape and Drawable
	processDrawableShape(rect)
	fmt.Println()
	processDrawableShape(circle)
	
	fmt.Println()
	
	// USING VEHICLE INTERFACE
	fmt.Println("=== Using Vehicle Interface ===")
	
	operateVehicle(car)
	fmt.Println()
	operateVehicle(motorcycle)
	
	fmt.Println()
	
	// INTERFACE SLICES
	fmt.Println("=== Interface Slices ===")
	
	// Slice of Shape interface can hold any type that implements Shape
	shapes := []Shape{
		Rectangle{Width: 4, Height: 6},
		Circle{Radius: 3},
		Rectangle{Width: 2, Height: 2},
		Circle{Radius: 1.5},
	}
	
	fmt.Println("Calculating areas of different shapes:")
	totalArea := 0.0
	for i, shape := range shapes {
		area := shape.Area()
		totalArea += area
		fmt.Printf("Shape %d: Area = %.2f\n", i+1, area)
	}
	fmt.Printf("Total area: %.2f\n", totalArea)
	
	fmt.Println()
	
	// INTERFACE VARIABLES
	fmt.Println("=== Interface Variables ===")
	
	// Interface variable can hold any type that implements the interface
	var vehicle Vehicle
	
	fmt.Println("Vehicle is initially nil:", vehicle == nil)
	
	// Assign a Car to the interface variable
	vehicle = car
	fmt.Printf("Vehicle is now a car: %T\n", vehicle)
	operateVehicle(vehicle)
	
	fmt.Println()
	
	// Assign a Motorcycle to the same interface variable
	vehicle = motorcycle
	fmt.Printf("Vehicle is now a motorcycle: %T\n", vehicle)
	operateVehicle(vehicle)
	
	fmt.Println()
	
	// POLYMORPHISM DEMONSTRATION
	fmt.Println("=== Polymorphism in Action ===")
	
	// Array of different vehicles
	vehicles := []Vehicle{
		Car{Brand: "Honda", Speed: 55},
		Motorcycle{Brand: "Yamaha", Speed: 75},
		Car{Brand: "Ford", Speed: 65},
		Motorcycle{Brand: "Ducati", Speed: 90},
	}
	
	fmt.Println("Operating different vehicles:")
	for i, v := range vehicles {
		fmt.Printf("\nVehicle %d (%T):\n", i+1, v)
		operateVehicle(v)
	}
	
	fmt.Println()
	
	// INTERFACE SATISFACTION CHECK
	fmt.Println("=== Interface Satisfaction ===")
	
	// Check if types implement interfaces at compile time
	var _ Shape = Rectangle{}    // Rectangle implements Shape
	var _ Shape = Circle{}       // Circle implements Shape
	var _ Vehicle = Car{}        // Car implements Vehicle
	var _ Vehicle = Motorcycle{} // Motorcycle implements Vehicle
	
	fmt.Println("All interface implementations verified at compile time!")
	
	// EMPTY INTERFACE PREVIEW
	fmt.Println("\n=== Empty Interface Preview ===")
	
	// interface{} can hold any type (we'll learn more about this next)
	var anything interface{}
	
	anything = 42
	fmt.Printf("anything = %v (type: %T)\n", anything, anything)
	
	anything = "Hello"
	fmt.Printf("anything = %v (type: %T)\n", anything, anything)
	
	anything = rect
	fmt.Printf("anything = %v (type: %T)\n", anything, anything)
	
	fmt.Println()
	fmt.Println("Interfaces demonstration complete! 🔌")
}
