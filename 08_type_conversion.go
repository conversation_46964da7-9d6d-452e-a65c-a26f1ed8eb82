// Package declaration
package main

// Import fmt for printing
import "fmt"

// Main function
func main() {
	// Sometimes you need to convert one type to another
	// This is called "type conversion"
	
	// Convert int to float64
	var wholeNumber int = 42
	var decimalNumber float64 = float64(wholeNumber)
	
	fmt.Println("Original int:", wholeNumber)
	fmt.Println("Converted to float64:", decimalNumber)
	
	// Convert float64 to int (loses decimal part)
	var price float64 = 19.99
	var roundedPrice int = int(price)
	
	fmt.Println("Original float64:", price)
	fmt.Println("Converted to int:", roundedPrice)
	
	// Convert numbers to strings (for display purposes)
	var age int = 25
	var ageText string = fmt.Sprintf("%d", age)
	
	fmt.Println("Age as number:", age)
	fmt.Println("Age as text:", ageText)
	
	// You can also use short declaration with type conversion
	height := 175.5
	heightInInt := int(height)
	
	fmt.Println("Height as float:", height)
	fmt.Println("Height as int:", heightInInt)
	
	// Boolean to string conversion
	isActive := true
	statusText := fmt.Sprintf("%t", isActive)
	
	fmt.Println("Status as bool:", isActive)
	fmt.Println("Status as text:", statusText)
}
