// Package declaration
package main

// Import packages
import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"
)

// FILE OPERATIONS EXAMPLES

// Function to check if file or directory exists
func exists(path string) bool {
	_, err := os.Stat(path)
	return !os.IsNotExist(err)
}

// Function to create sample files and directories for demonstration
func setupTestEnvironment() error {
	fmt.Println("=== Setting up test environment ===")

	// Create test directory
	if err := os.MkdirAll("testdir", 0755); err != nil {
		return fmt.Errorf("failed to create test directory: %w", err)
	}

	// Create sample files
	files := map[string]string{
		"source.txt":         "This is the source file content.\nIt has multiple lines.\n",
		"testdir/nested.txt": "This file is in a subdirectory.\n",
		"data.csv":           "name,age\nAlice,25\nBob,30\n",
		"config.json":        `{"server": "localhost", "port": 8080}`,
	}

	for filename, content := range files {
		// Create directory if needed
		dir := filepath.Dir(filename)
		if dir != "." {
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("failed to create directory %s: %w", dir, err)
			}
		}

		// Write file
		if err := os.WriteFile(filename, []byte(content), 0644); err != nil {
			return fmt.Errorf("failed to create file %s: %w", filename, err)
		}
	}

	fmt.Println("✅ Test environment created")
	return nil
}

// Function to copy a file
func copyFile(src, dst string) error {
	fmt.Printf("=== Copying file: %s → %s ===\n", src, dst)

	// Open source file
	sourceFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("failed to open source file %s: %w", src, err)
	}
	defer sourceFile.Close()

	// Create destination file
	destFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("failed to create destination file %s: %w", dst, err)
	}
	defer destFile.Close()

	// Copy content
	bytesWritten, err := io.Copy(destFile, sourceFile)
	if err != nil {
		return fmt.Errorf("failed to copy content: %w", err)
	}

	fmt.Printf("✅ Copied %d bytes from %s to %s\n", bytesWritten, src, dst)
	return nil
}

// Function to move/rename a file
func moveFile(src, dst string) error {
	fmt.Printf("=== Moving file: %s → %s ===\n", src, dst)

	// Use os.Rename for moving/renaming
	err := os.Rename(src, dst)
	if err != nil {
		return fmt.Errorf("failed to move file from %s to %s: %w", src, dst, err)
	}

	fmt.Printf("✅ Moved %s to %s\n", src, dst)
	return nil
}

// Function to delete files and directories
func deleteFile(path string) error {
	fmt.Printf("=== Deleting: %s ===\n", path)

	// Check if it's a directory
	info, err := os.Stat(path)
	if err != nil {
		return fmt.Errorf("failed to get info for %s: %w", path, err)
	}

	if info.IsDir() {
		// Remove directory and all contents
		err = os.RemoveAll(path)
		if err != nil {
			return fmt.Errorf("failed to remove directory %s: %w", path, err)
		}
		fmt.Printf("✅ Removed directory %s and all contents\n", path)
	} else {
		// Remove file
		err = os.Remove(path)
		if err != nil {
			return fmt.Errorf("failed to remove file %s: %w", path, err)
		}
		fmt.Printf("✅ Removed file %s\n", path)
	}

	return nil
}

// Function to create directories
func createDirectories() error {
	fmt.Println("=== Creating directories ===")

	directories := []string{
		"newdir",
		"nested/deep/directory",
		"another/path",
	}

	for _, dir := range directories {
		// Create directory and all parent directories
		err := os.MkdirAll(dir, 0755)
		if err != nil {
			return fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
		fmt.Printf("✅ Created directory: %s\n", dir)
	}

	return nil
}

// Function to get file information
func getFileInfo(path string) error {
	fmt.Printf("=== File information for: %s ===\n", path)

	info, err := os.Stat(path)
	if err != nil {
		return fmt.Errorf("failed to get file info for %s: %w", path, err)
	}

	fmt.Printf("Name: %s\n", info.Name())
	fmt.Printf("Size: %d bytes\n", info.Size())
	fmt.Printf("Mode: %s\n", info.Mode())
	fmt.Printf("ModTime: %s\n", info.ModTime().Format("2006-01-02 15:04:05"))
	fmt.Printf("IsDir: %t\n", info.IsDir())

	// Additional file mode information
	mode := info.Mode()
	fmt.Printf("Permissions: %o\n", mode.Perm())
	fmt.Printf("Is regular file: %t\n", mode.IsRegular())

	return nil
}

// Function to list directory contents
func listDirectory(dirPath string) error {
	fmt.Printf("=== Listing directory: %s ===\n", dirPath)

	// Read directory entries
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return fmt.Errorf("failed to read directory %s: %w", dirPath, err)
	}

	fmt.Printf("Contents of %s:\n", dirPath)
	for _, entry := range entries {
		info, err := entry.Info()
		if err != nil {
			fmt.Printf("  ❌ Error getting info for %s: %v\n", entry.Name(), err)
			continue
		}

		fileType := "FILE"
		if entry.IsDir() {
			fileType = "DIR "
		}

		fmt.Printf("  %s %8d bytes %s %s\n",
			fileType,
			info.Size(),
			info.ModTime().Format("2006-01-02 15:04"),
			entry.Name())
	}

	return nil
}

// Function to walk directory tree
func walkDirectory(rootPath string) error {
	fmt.Printf("=== Walking directory tree: %s ===\n", rootPath)

	err := filepath.Walk(rootPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			fmt.Printf("❌ Error accessing %s: %v\n", path, err)
			return nil // Continue walking
		}

		// Calculate relative path
		relPath, _ := filepath.Rel(rootPath, path)
		if relPath == "." {
			relPath = rootPath
		}

		// Determine file type
		fileType := "FILE"
		if info.IsDir() {
			fileType = "DIR "
		}

		// Calculate depth for indentation
		depth := len(filepath.SplitList(relPath)) - 1
		indent := ""
		for i := 0; i < depth; i++ {
			indent += "  "
		}

		fmt.Printf("%s%s %s (%d bytes)\n",
			indent, fileType, filepath.Base(path), info.Size())

		return nil
	})

	if err != nil {
		return fmt.Errorf("error walking directory %s: %w", rootPath, err)
	}

	return nil
}

// Function to demonstrate file path operations
func demonstratePathOperations() {
	fmt.Println("=== Path Operations ===")

	paths := []string{
		"testdir/nested.txt",
		"/home/<USER>/documents/file.pdf",
		"../parent/file.txt",
		"simple.txt",
	}

	for _, path := range paths {
		fmt.Printf("\nPath: %s\n", path)
		fmt.Printf("  Dir: %s\n", filepath.Dir(path))
		fmt.Printf("  Base: %s\n", filepath.Base(path))
		fmt.Printf("  Ext: %s\n", filepath.Ext(path))
		fmt.Printf("  IsAbs: %t\n", filepath.IsAbs(path))

		// Clean the path
		cleaned := filepath.Clean(path)
		fmt.Printf("  Cleaned: %s\n", cleaned)

		// Join with another path
		joined := filepath.Join("root", path)
		fmt.Printf("  Joined with 'root': %s\n", joined)
	}
}

// Main function
func main() {
	fmt.Println("=== FILE OPERATIONS IN GO ===")
	fmt.Println()

	// Setup test environment
	if err := setupTestEnvironment(); err != nil {
		fmt.Printf("❌ Setup error: %v\n", err)
		return
	}
	fmt.Println()

	// FILE EXISTENCE CHECK
	fmt.Println("=== Checking file existence ===")
	testPaths := []string{"source.txt", "nonexistent.txt", "testdir", "testdir/nested.txt"}

	for _, path := range testPaths {
		if exists(path) {
			fmt.Printf("✅ %s exists\n", path)
		} else {
			fmt.Printf("❌ %s does not exist\n", path)
		}
	}
	fmt.Println()

	// COPY FILE
	if err := copyFile("source.txt", "copy_of_source.txt"); err != nil {
		fmt.Printf("❌ Copy error: %v\n", err)
	}
	fmt.Println()

	// MOVE/RENAME FILE
	if err := moveFile("copy_of_source.txt", "renamed_source.txt"); err != nil {
		fmt.Printf("❌ Move error: %v\n", err)
	}
	fmt.Println()

	// CREATE DIRECTORIES
	if err := createDirectories(); err != nil {
		fmt.Printf("❌ Directory creation error: %v\n", err)
	}
	fmt.Println()

	// GET FILE INFORMATION
	infoFiles := []string{"source.txt", "testdir", "data.csv"}
	for _, file := range infoFiles {
		if err := getFileInfo(file); err != nil {
			fmt.Printf("❌ Info error: %v\n", err)
		}
		fmt.Println()
	}

	// LIST DIRECTORY CONTENTS
	if err := listDirectory("."); err != nil {
		fmt.Printf("❌ List error: %v\n", err)
	}
	fmt.Println()

	if err := listDirectory("testdir"); err != nil {
		fmt.Printf("❌ List error: %v\n", err)
	}
	fmt.Println()

	// WALK DIRECTORY TREE
	if err := walkDirectory("."); err != nil {
		fmt.Printf("❌ Walk error: %v\n", err)
	}
	fmt.Println()

	// PATH OPERATIONS
	demonstratePathOperations()
	fmt.Println()

	// FILE SIZE AND MODIFICATION TIME
	fmt.Println("=== File Statistics ===")
	if info, err := os.Stat("source.txt"); err == nil {
		fmt.Printf("source.txt: %d bytes, modified %s\n",
			info.Size(), info.ModTime().Format("2006-01-02 15:04:05"))

		// Check if file was modified in the last minute
		if time.Since(info.ModTime()) < time.Minute {
			fmt.Println("  → File was recently modified")
		}
	}
	fmt.Println()

	// CLEANUP DEMONSTRATION
	fmt.Println("=== Cleanup Operations ===")

	// Delete specific files
	filesToDelete := []string{"renamed_source.txt", "data.csv"}
	for _, file := range filesToDelete {
		if exists(file) {
			if err := deleteFile(file); err != nil {
				fmt.Printf("❌ Delete error: %v\n", err)
			}
		}
	}

	// Delete directories
	dirsToDelete := []string{"newdir", "nested", "another"}
	for _, dir := range dirsToDelete {
		if exists(dir) {
			if err := deleteFile(dir); err != nil {
				fmt.Printf("❌ Delete error: %v\n", err)
			}
		}
	}
	fmt.Println()

	// BEST PRACTICES
	fmt.Println("=== File Operations Best Practices ===")
	fmt.Println("1. Always check for errors in file operations")
	fmt.Println("2. Use filepath.Join for cross-platform path handling")
	fmt.Println("3. Check file existence before operations when needed")
	fmt.Println("4. Use os.MkdirAll to create nested directories")
	fmt.Println("5. Use os.RemoveAll for recursive directory deletion")
	fmt.Println("6. Be careful with file permissions (especially on Unix)")
	fmt.Println("7. Use filepath.Walk for recursive directory traversal")
	fmt.Println("8. Handle both files and directories appropriately")

	fmt.Println()
	fmt.Println("File operations demonstration complete! 🗂️")
	fmt.Println("Key takeaway: Go provides comprehensive file system operations!")

	// Note about remaining files
	fmt.Println("\nNote: Some test files remain for examination:")
	fmt.Println("- source.txt, config.json, testdir/")
	fmt.Println("You can delete them manually when done.")
}
