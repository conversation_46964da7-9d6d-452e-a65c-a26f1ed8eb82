// Package declaration
package main

// Import packages
import (
	"fmt"
	"time"
)

// BASIC FUNCTIONS FOR DEMONSTRATION

// Simple function that prints numbers
func printNumbers(name string) {
	for i := 1; i <= 5; i++ {
		fmt.Printf("%s: %d\n", name, i)
		time.Sleep(100 * time.Millisecond) // Sleep for 100ms
	}
	fmt.Printf("%s finished!\n", name)
}

// Function that simulates work
func doWork(workerName string, workTime time.Duration) {
	fmt.Printf("%s started working...\n", workerName)
	time.Sleep(workTime) // Simulate work by sleeping
	fmt.Printf("%s finished work after %v\n", workerName, workTime)
}

// Function that counts and returns when done
func countToN(name string, n int) {
	fmt.Printf("%s: Starting to count to %d\n", name, n)
	
	for i := 1; i <= n; i++ {
		fmt.Printf("%s: %d\n", name, i)
		time.Sleep(50 * time.Millisecond)
	}
	
	fmt.Printf("%s: Finished counting to %d\n", name, n)
}

// Function that simulates downloading a file
func downloadFile(filename string, size int) {
	fmt.Printf("📥 Starting download: %s (%d MB)\n", filename, size)
	
	// Simulate download progress
	for progress := 10; progress <= 100; progress += 10 {
		time.Sleep(100 * time.Millisecond) // Simulate download time
		fmt.Printf("📥 %s: %d%% complete\n", filename, progress)
	}
	
	fmt.Printf("✅ Download complete: %s\n", filename)
}

// Function that processes data
func processData(dataName string, processingTime time.Duration) {
	fmt.Printf("🔄 Processing %s...\n", dataName)
	
	// Simulate processing steps
	steps := []string{"Loading", "Analyzing", "Transforming", "Saving"}
	
	for _, step := range steps {
		fmt.Printf("🔄 %s: %s...\n", dataName, step)
		time.Sleep(processingTime / 4) // Divide time among steps
	}
	
	fmt.Printf("✅ %s processing complete!\n", dataName)
}

// Main function
func main() {
	fmt.Println("=== GOROUTINES IN GO ===")
	fmt.Println()
	
	// SEQUENTIAL EXECUTION (without goroutines)
	fmt.Println("=== Sequential Execution ===")
	fmt.Println("Running functions one after another...")
	
	start := time.Now()
	
	printNumbers("Sequential-1")
	printNumbers("Sequential-2")
	
	sequential_duration := time.Since(start)
	fmt.Printf("Sequential execution took: %v\n", sequential_duration)
	fmt.Println()
	
	// CONCURRENT EXECUTION (with goroutines)
	fmt.Println("=== Concurrent Execution with Goroutines ===")
	fmt.Println("Running functions simultaneously...")
	
	start = time.Now()
	
	// The 'go' keyword starts a goroutine
	go printNumbers("Goroutine-1")
	go printNumbers("Goroutine-2")
	
	// Wait for goroutines to finish
	// In real code, you'd use sync.WaitGroup or channels
	time.Sleep(1 * time.Second)
	
	concurrent_duration := time.Since(start)
	fmt.Printf("Concurrent execution took: %v\n", concurrent_duration)
	fmt.Printf("Time saved: %v\n", sequential_duration-concurrent_duration)
	fmt.Println()
	
	// MULTIPLE GOROUTINES
	fmt.Println("=== Multiple Goroutines ===")
	fmt.Println("Starting multiple workers...")
	
	// Start multiple goroutines with different work times
	go doWork("Worker-A", 300*time.Millisecond)
	go doWork("Worker-B", 500*time.Millisecond)
	go doWork("Worker-C", 200*time.Millisecond)
	go doWork("Worker-D", 400*time.Millisecond)
	
	// Wait for all workers to finish
	time.Sleep(600 * time.Millisecond)
	fmt.Println("All workers finished!")
	fmt.Println()
	
	// GOROUTINES WITH DIFFERENT TASKS
	fmt.Println("=== Different Tasks Concurrently ===")
	
	// Start different types of work simultaneously
	go countToN("Counter", 8)
	go downloadFile("document.pdf", 25)
	go processData("UserData", 400*time.Millisecond)
	
	// Wait for all tasks to complete
	time.Sleep(1 * time.Second)
	fmt.Println("All tasks completed!")
	fmt.Println()
	
	// GOROUTINES IN LOOPS
	fmt.Println("=== Goroutines in Loops ===")
	fmt.Println("Starting multiple download tasks...")
	
	files := []struct {
		name string
		size int
	}{
		{"image1.jpg", 5},
		{"video.mp4", 50},
		{"document.pdf", 10},
		{"archive.zip", 25},
	}
	
	// Start a goroutine for each file download
	for _, file := range files {
		go downloadFile(file.name, file.size)
	}
	
	// Wait for all downloads to complete
	time.Sleep(1200 * time.Millisecond)
	fmt.Println("All downloads completed!")
	fmt.Println()
	
	// ANONYMOUS GOROUTINES
	fmt.Println("=== Anonymous Goroutines ===")
	
	// You can start goroutines with anonymous functions
	go func() {
		fmt.Println("Anonymous goroutine 1: Starting...")
		time.Sleep(200 * time.Millisecond)
		fmt.Println("Anonymous goroutine 1: Finished!")
	}()
	
	go func(message string) {
		fmt.Printf("Anonymous goroutine 2: %s\n", message)
		time.Sleep(300 * time.Millisecond)
		fmt.Println("Anonymous goroutine 2: Done!")
	}("Hello from anonymous function!")
	
	// Goroutine with closure (captures variables)
	name := "Closure Example"
	go func() {
		fmt.Printf("Anonymous goroutine 3: %s\n", name)
		time.Sleep(100 * time.Millisecond)
		fmt.Println("Anonymous goroutine 3: Completed!")
	}()
	
	// Wait for anonymous goroutines
	time.Sleep(400 * time.Millisecond)
	fmt.Println()
	
	// GOROUTINE PERFORMANCE DEMONSTRATION
	fmt.Println("=== Goroutine Performance ===")
	
	// Function to simulate light work
	lightWork := func(id int) {
		fmt.Printf("Goroutine %d: Working...\n", id)
		time.Sleep(10 * time.Millisecond)
		fmt.Printf("Goroutine %d: Done!\n", id)
	}
	
	// Start many goroutines (Go can handle thousands easily)
	fmt.Println("Starting 10 goroutines simultaneously...")
	
	start = time.Now()
	
	for i := 1; i <= 10; i++ {
		go lightWork(i)
	}
	
	// Wait for all to complete
	time.Sleep(50 * time.Millisecond)
	
	fmt.Printf("10 goroutines completed in: %v\n", time.Since(start))
	fmt.Println()
	
	// IMPORTANT NOTES DEMONSTRATION
	fmt.Println("=== Important Notes ===")
	
	fmt.Println("1. Main function doesn't wait for goroutines automatically")
	fmt.Println("2. If main function exits, all goroutines are terminated")
	fmt.Println("3. Goroutines are very lightweight (2KB stack initially)")
	fmt.Println("4. You can have thousands of goroutines efficiently")
	fmt.Println("5. Use channels or sync.WaitGroup for proper synchronization")
	
	// Demonstrate main function not waiting
	fmt.Println("\nDemonstrating main not waiting:")
	
	go func() {
		fmt.Println("This goroutine might not finish...")
		time.Sleep(100 * time.Millisecond)
		fmt.Println("...if main exits too early!")
	}()
	
	// Without this sleep, the above goroutine might not complete
	fmt.Println("Main function waiting a bit...")
	time.Sleep(150 * time.Millisecond)
	
	fmt.Println()
	fmt.Println("Goroutines demonstration complete! 🚀")
	fmt.Println("Next: Learn about channels for goroutine communication!")
}
