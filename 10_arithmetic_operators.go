// Package declaration
package main

// Import fmt for printing
import "fmt"

// Main function
func main() {
	// ARITHMETIC OPERATORS
	// These operators perform mathematical calculations
	
	// Basic numbers to work with
	a := 20
	b := 6
	
	// Addition operator (+)
	// Adds two numbers together
	sum := a + b
	fmt.Println("Addition:", a, "+", b, "=", sum)
	
	// Subtraction operator (-)
	// Subtracts second number from first
	difference := a - b
	fmt.Println("Subtraction:", a, "-", b, "=", difference)
	
	// Multiplication operator (*)
	// Multiplies two numbers
	product := a * b
	fmt.Println("Multiplication:", a, "*", b, "=", product)
	
	// Division operator (/)
	// Divides first number by second
	quotient := a / b
	fmt.Println("Division:", a, "/", b, "=", quotient)
	
	// Modulus operator (%)
	// Gives the remainder after division
	remainder := a % b
	fmt.Println("Modulus:", a, "%", b, "=", remainder)
	
	fmt.Println()
	
	// Working with float numbers
	price := 19.99
	taxRate := 0.08
	
	// Calculate tax amount
	taxAmount := price * taxRate
	fmt.Println("Price: $", price)
	fmt.Println("Tax Rate:", taxRate * 100, "%")
	fmt.Println("Tax Amount: $", taxAmount)
	
	// Calculate total price
	totalPrice := price + taxAmount
	fmt.Println("Total Price: $", totalPrice)
	
	fmt.Println()
	
	// Increment and Decrement operators
	counter := 10
	fmt.Println("Original counter:", counter)
	
	// Increment (add 1)
	counter++
	fmt.Println("After increment:", counter)
	
	// Decrement (subtract 1)
	counter--
	fmt.Println("After decrement:", counter)
}
