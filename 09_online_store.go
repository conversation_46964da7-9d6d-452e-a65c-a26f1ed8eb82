// Package declaration
package main

// Import fmt for printing
import "fmt"

// Main function
func main() {
	// Online Store Product Information
	// Using different data types for different kinds of information
	
	// Product details (strings for text information)
	productName := "Wireless Headphones"
	brand := "TechSound"
	category := "Electronics"
	description := "High-quality wireless headphones with noise cancellation"
	
	// Pricing information (float64 for money)
	originalPrice := 199.99
	discountPercent := 15.0
	shippingCost := 9.99
	
	// Inventory details (int for counting)
	stockQuantity := 47
	soldToday := 12
	reviewCount := 234
	
	// Product features (bool for yes/no questions)
	isInStock := true
	hasWarranty := true
	isFreeShipping := false
	isOnSale := true
	
	// Calculate discounted price
	discountAmount := originalPrice * (discountPercent / 100.0)
	finalPrice := originalPrice - discountAmount
	
	// Display product information
	fmt.Println("=== ONLINE STORE PRODUCT ===")
	fmt.Println("Product:", productName)
	fmt.Println("Brand:", brand)
	fmt.Println("Category:", category)
	fmt.Println("Description:", description)
	fmt.Println()
	
	fmt.Println("=== PRICING ===")
	fmt.Println("Original Price: $", originalPrice)
	fmt.Println("Discount:", discountPercent, "%")
	fmt.Println("You Save: $", discountAmount)
	fmt.Println("Final Price: $", finalPrice)
	fmt.Println("Shipping: $", shippingCost)
	fmt.Println()
	
	fmt.Println("=== INVENTORY ===")
	fmt.Println("In Stock:", stockQuantity, "units")
	fmt.Println("Sold Today:", soldToday, "units")
	fmt.Println("Customer Reviews:", reviewCount)
	fmt.Println()
	
	fmt.Println("=== FEATURES ===")
	fmt.Println("Available:", isInStock)
	fmt.Println("Warranty Included:", hasWarranty)
	fmt.Println("Free Shipping:", isFreeShipping)
	fmt.Println("On Sale:", isOnSale)
}
