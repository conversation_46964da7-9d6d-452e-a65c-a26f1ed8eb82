// Package declaration
package main

// Import fmt for formatting
import "fmt"

// Main function
func main() {
	// FORMATTED OUTPUT with fmt.Printf
	// This allows you to control exactly how your output looks
	
	// Sample data
	productName := "Laptop"
	price := 999.99
	quantity := 3
	inStock := true
	discount := 15.5
	
	fmt.Println("=== FORMATTING EXAMPLES ===")
	
	// %s - for strings
	fmt.Printf("Product: %s\n", productName)
	
	// %d - for integers
	fmt.Printf("Quantity: %d\n", quantity)
	
	// %f - for floats (default 6 decimal places)
	fmt.Printf("Price (default): %f\n", price)
	
	// %.2f - for floats with 2 decimal places
	fmt.Printf("Price (2 decimals): %.2f\n", price)
	
	// %t - for booleans (true/false)
	fmt.Printf("In stock: %t\n", inStock)
	
	// %% - to print an actual % symbol
	fmt.Printf("Discount: %.1f%%\n", discount)
	
	fmt.Println()
	
	// ADVANCED FORMATTING
	fmt.Println("=== ADVANCED FORMATTING ===")
	
	// Width and alignment
	fmt.Printf("%-15s: $%8.2f\n", "Laptop", 999.99)      // Left-aligned string, right-aligned number
	fmt.Printf("%-15s: $%8.2f\n", "Mouse", 29.99)        // Same format for consistency
	fmt.Printf("%-15s: $%8.2f\n", "Keyboard", 79.50)
	
	fmt.Println()
	
	// Creating a simple receipt
	fmt.Println("=== RECEIPT ===")
	fmt.Printf("%-20s %5s %10s\n", "Item", "Qty", "Price")
	fmt.Printf("%-20s %5s %10s\n", "----", "---", "-----")
	fmt.Printf("%-20s %5d $%9.2f\n", "Wireless Mouse", 2, 59.98)
	fmt.Printf("%-20s %5d $%9.2f\n", "USB Cable", 1, 12.99)
	fmt.Printf("%-20s %5d $%9.2f\n", "Laptop Stand", 1, 45.00)
	fmt.Printf("%-20s %5s %10s\n", "----", "---", "-----")
	
	total := 59.98 + 12.99 + 45.00
	fmt.Printf("%-20s %5s $%9.2f\n", "TOTAL", "", total)
	
	fmt.Println()
	
	// Using fmt.Sprintf to create formatted strings (doesn't print, just creates)
	message := fmt.Sprintf("Hello %s, your order of %d items costs $%.2f", "John", 3, 117.97)
	fmt.Println("Created message:", message)
}
