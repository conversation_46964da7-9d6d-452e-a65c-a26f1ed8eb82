// Package declaration
package main

// Import packages
import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
)

// CREATING GO MODULES DEMONSTRATION

// Function to run shell commands and display output
func runCommand(name string, args ...string) error {
	fmt.Printf("Running: %s %v\n", name, args)
	
	cmd := exec.Command(name, args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("command failed: %w", err)
	}
	
	fmt.Println()
	return nil
}

// Function to create a file with content
func createFile(filename, content string) error {
	err := os.WriteFile(filename, []byte(content), 0644)
	if err != nil {
		return fmt.Errorf("failed to create file %s: %w", filename, err)
	}
	
	fmt.Printf("✅ Created file: %s\n", filename)
	return nil
}

// Function to create directory structure
func createDirectory(path string) error {
	err := os.Mkdir<PERSON>ll(path, 0755)
	if err != nil {
		return fmt.Errorf("failed to create directory %s: %w", path, err)
	}
	
	fmt.Printf("✅ Created directory: %s\n", path)
	return nil
}

// Function to demonstrate module creation
func demonstrateModuleCreation() error {
	fmt.Println("=== Creating a Go Module ===")
	
	// Create project directory
	projectDir := "myproject"
	if err := createDirectory(projectDir); err != nil {
		return err
	}
	
	// Change to project directory
	originalDir, _ := os.Getwd()
	defer os.Chdir(originalDir)
	
	if err := os.Chdir(projectDir); err != nil {
		return fmt.Errorf("failed to change directory: %w", err)
	}
	
	// Initialize Go module
	fmt.Println("\n--- Initializing Go Module ---")
	if err := runCommand("go", "mod", "init", "github.com/username/myproject"); err != nil {
		return err
	}
	
	// Create main.go file
	mainContent := `package main

import (
	"fmt"
	"github.com/username/myproject/calculator"
	"github.com/username/myproject/utils"
)

func main() {
	fmt.Println("=== My Go Project ===")
	
	// Use calculator package
	result := calculator.Add(10, 5)
	fmt.Printf("10 + 5 = %d\n", result)
	
	result = calculator.Multiply(4, 7)
	fmt.Printf("4 * 7 = %d\n", result)
	
	// Use utils package
	greeting := utils.FormatGreeting("Alice")
	fmt.Println(greeting)
	
	if utils.IsEven(result) {
		fmt.Printf("%d is even\n", result)
	} else {
		fmt.Printf("%d is odd\n", result)
	}
}`
	
	if err := createFile("main.go", mainContent); err != nil {
		return err
	}
	
	// Create calculator package
	if err := createDirectory("calculator"); err != nil {
		return err
	}
	
	calculatorContent := `// Package calculator provides basic mathematical operations
package calculator

// Add returns the sum of two integers
func Add(a, b int) int {
	return a + b
}

// Subtract returns the difference of two integers
func Subtract(a, b int) int {
	return a - b
}

// Multiply returns the product of two integers
func Multiply(a, b int) int {
	return a * b
}

// Divide returns the quotient of two integers
// Returns 0 if divisor is 0
func Divide(a, b int) int {
	if b == 0 {
		return 0
	}
	return a / b
}`
	
	if err := createFile("calculator/calculator.go", calculatorContent); err != nil {
		return err
	}
	
	// Create utils package
	if err := createDirectory("utils"); err != nil {
		return err
	}
	
	utilsContent := `// Package utils provides utility functions
package utils

import "fmt"

// FormatGreeting creates a formatted greeting message
func FormatGreeting(name string) string {
	return fmt.Sprintf("Hello, %s! Welcome to our application.", name)
}

// IsEven checks if a number is even
func IsEven(n int) bool {
	return n%2 == 0
}

// IsOdd checks if a number is odd
func IsOdd(n int) bool {
	return n%2 != 0
}

// Max returns the larger of two integers
func Max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// Min returns the smaller of two integers
func Min(a, b int) int {
	if a < b {
		return a
	}
	return b
}`
	
	if err := createFile("utils/utils.go", utilsContent); err != nil {
		return err
	}
	
	// Show go.mod file content
	fmt.Println("--- go.mod file content ---")
	if err := runCommand("cat", "go.mod"); err != nil {
		return err
	}
	
	// Run go mod tidy to clean up
	fmt.Println("--- Running go mod tidy ---")
	if err := runCommand("go", "mod", "tidy"); err != nil {
		return err
	}
	
	// Build the project
	fmt.Println("--- Building the project ---")
	if err := runCommand("go", "build", "."); err != nil {
		return err
	}
	
	// Run the project
	fmt.Println("--- Running the project ---")
	if err := runCommand("go", "run", "."); err != nil {
		return err
	}
	
	return nil
}

// Function to demonstrate working with external dependencies
func demonstrateExternalDependencies() error {
	fmt.Println("=== Working with External Dependencies ===")
	
	// Create a new project for external dependencies demo
	projectDir := "webproject"
	if err := createDirectory(projectDir); err != nil {
		return err
	}
	
	// Change to project directory
	originalDir, _ := os.Getwd()
	defer os.Chdir(originalDir)
	
	if err := os.Chdir(projectDir); err != nil {
		return fmt.Errorf("failed to change directory: %w", err)
	}
	
	// Initialize Go module
	fmt.Println("\n--- Initializing Web Project Module ---")
	if err := runCommand("go", "mod", "init", "github.com/username/webproject"); err != nil {
		return err
	}
	
	// Create a simple web server that uses external dependencies
	webServerContent := `package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"
)

// Response represents a JSON response
type Response struct {
	Message   string    ` + "`json:\"message\"`" + `
	Timestamp time.Time ` + "`json:\"timestamp\"`" + `
	Status    string    ` + "`json:\"status\"`" + `
}

// Handler for the root endpoint
func homeHandler(w http.ResponseWriter, r *http.Request) {
	response := Response{
		Message:   "Welcome to our Go web server!",
		Timestamp: time.Now(),
		Status:    "success",
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// Handler for the health check endpoint
func healthHandler(w http.ResponseWriter, r *http.Request) {
	response := Response{
		Message:   "Server is healthy",
		Timestamp: time.Now(),
		Status:    "ok",
	}
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func main() {
	fmt.Println("Starting web server on :8080")
	
	http.HandleFunc("/", homeHandler)
	http.HandleFunc("/health", healthHandler)
	
	log.Fatal(http.ListenAndServe(":8080", nil))
}`
	
	if err := createFile("main.go", webServerContent); err != nil {
		return err
	}
	
	// Show initial go.mod
	fmt.Println("--- Initial go.mod ---")
	if err := runCommand("cat", "go.mod"); err != nil {
		return err
	}
	
	// Add external dependency (this would normally download packages)
	fmt.Println("--- Adding external dependency (simulated) ---")
	fmt.Println("In a real scenario, you would run:")
	fmt.Println("go get github.com/gorilla/mux")
	fmt.Println("go get github.com/sirupsen/logrus")
	
	// Run go mod tidy
	fmt.Println("--- Running go mod tidy ---")
	if err := runCommand("go", "mod", "tidy"); err != nil {
		return err
	}
	
	// Build the project
	fmt.Println("--- Building web project ---")
	if err := runCommand("go", "build", "."); err != nil {
		return err
	}
	
	fmt.Println("✅ Web project built successfully")
	fmt.Println("Note: To run the server, execute: go run . (then visit http://localhost:8080)")
	
	return nil
}

// Function to show module commands
func showModuleCommands() {
	fmt.Println("=== Essential Go Module Commands ===")
	
	commands := []struct {
		command     string
		description string
	}{
		{"go mod init <module-name>", "Initialize a new module"},
		{"go mod tidy", "Add missing and remove unused modules"},
		{"go mod download", "Download modules to local cache"},
		{"go mod verify", "Verify dependencies have expected content"},
		{"go get <package>", "Add dependency to current module"},
		{"go get <package>@version", "Add specific version of dependency"},
		{"go get -u <package>", "Update dependency to latest version"},
		{"go list -m all", "List all modules in current build"},
		{"go mod graph", "Print module requirement graph"},
		{"go mod why <package>", "Explain why package is needed"},
		{"go clean -modcache", "Remove all downloaded modules"},
	}
	
	for _, cmd := range commands {
		fmt.Printf("%-30s - %s\n", cmd.command, cmd.description)
	}
}

// Main function
func main() {
	fmt.Println("=== GO MODULES DEMONSTRATION ===")
	fmt.Println()
	
	// Show module commands
	showModuleCommands()
	fmt.Println()
	
	// Demonstrate module creation
	if err := demonstrateModuleCreation(); err != nil {
		fmt.Printf("❌ Module creation error: %v\n", err)
	}
	fmt.Println()
	
	// Demonstrate external dependencies
	if err := demonstrateExternalDependencies(); err != nil {
		fmt.Printf("❌ External dependencies error: %v\n", err)
	}
	fmt.Println()
	
	// Best practices
	fmt.Println("=== Go Module Best Practices ===")
	fmt.Println("1. Use semantic versioning (v1.2.3)")
	fmt.Println("2. Choose descriptive module names")
	fmt.Println("3. Use go mod tidy regularly")
	fmt.Println("4. Commit go.mod and go.sum files")
	fmt.Println("5. Use replace directive for local development")
	fmt.Println("6. Keep dependencies up to date")
	fmt.Println("7. Use go mod vendor for reproducible builds")
	fmt.Println("8. Document your module with README.md")
	
	fmt.Println()
	
	// Module structure example
	fmt.Println("=== Typical Module Structure ===")
	fmt.Println("myproject/")
	fmt.Println("├── go.mod")
	fmt.Println("├── go.sum")
	fmt.Println("├── main.go")
	fmt.Println("├── README.md")
	fmt.Println("├── pkg/")
	fmt.Println("│   ├── calculator/")
	fmt.Println("│   │   └── calculator.go")
	fmt.Println("│   └── utils/")
	fmt.Println("│       └── utils.go")
	fmt.Println("├── cmd/")
	fmt.Println("│   └── server/")
	fmt.Println("│       └── main.go")
	fmt.Println("├── internal/")
	fmt.Println("│   └── config/")
	fmt.Println("│       └── config.go")
	fmt.Println("└── test/")
	fmt.Println("    └── integration/")
	fmt.Println("        └── api_test.go")
	
	fmt.Println()
	fmt.Println("Go modules demonstration complete! 📦")
	fmt.Println("Key takeaway: Modules organize code and manage dependencies!")
	
	// Cleanup note
	fmt.Println("\nNote: Created directories 'myproject' and 'webproject' for demonstration.")
	fmt.Println("You can explore them and delete when no longer needed.")
}
