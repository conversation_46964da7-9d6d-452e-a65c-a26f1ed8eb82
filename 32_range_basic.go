// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// RANGE KEYWORD - Iterate over collections
	
	// RANGE with SLICES
	fmt.Println("=== Range with Slices ===")
	
	fruits := []string{"apple", "banana", "orange", "grape"}
	
	// Range gives you index and value
	fmt.Println("Method 1: Both index and value")
	for index, fruit := range fruits {
		fmt.Printf("Index %d: %s\n", index, fruit)
	}
	
	// Range with only values (ignore index with _)
	fmt.Println("\nMethod 2: Only values")
	for _, fruit := range fruits {
		fmt.Printf("Fruit: %s\n", fruit)
	}
	
	// Range with only indexes (ignore value)
	fmt.Println("\nMethod 3: Only indexes")
	for index := range fruits {
		fmt.Printf("Index: %d\n", index)
	}
	
	fmt.Println()
	
	// RANGE with ARRAYS
	fmt.Println("=== Range with Arrays ===")
	
	numbers := [5]int{10, 20, 30, 40, 50}
	
	fmt.Println("Array elements:")
	for i, num := range numbers {
		fmt.Printf("Position %d: %d\n", i, num)
	}
	
	fmt.Println()
	
	// RANGE with MAPS
	fmt.Println("=== Range with Maps ===")
	
	ages := map[string]int{
		"Alice":   25,
		"Bob":     30,
		"Charlie": 22,
	}
	
	// Range gives you key and value
	fmt.Println("Method 1: Both key and value")
	for name, age := range ages {
		fmt.Printf("%s is %d years old\n", name, age)
	}
	
	// Range with only keys
	fmt.Println("\nMethod 2: Only keys")
	for name := range ages {
		fmt.Printf("Name: %s\n", name)
	}
	
	// Range with only values (ignore key with _)
	fmt.Println("\nMethod 3: Only values")
	for _, age := range ages {
		fmt.Printf("Age: %d\n", age)
	}
	
	fmt.Println()
	
	// RANGE with STRINGS
	fmt.Println("=== Range with Strings ===")
	
	text := "Hello"
	
	// Range over string gives you byte index and rune (character)
	fmt.Println("Characters in 'Hello':")
	for index, char := range text {
		fmt.Printf("Index %d: %c (Unicode: %d)\n", index, char, char)
	}
	
	// Range with Unicode characters
	unicode_text := "Hello 世界" // "Hello World" in Chinese
	fmt.Println("\nUnicode string:")
	for index, char := range unicode_text {
		fmt.Printf("Index %d: %c\n", index, char)
	}
	
	fmt.Println()
	
	// PRACTICAL EXAMPLES
	fmt.Println("=== Practical Examples ===")
	
	// Example 1: Calculate sum using range
	scores := []int{85, 92, 78, 96, 88}
	total := 0
	
	fmt.Println("Calculating total score:")
	for i, score := range scores {
		total += score
		fmt.Printf("Adding score %d (position %d): running total = %d\n", 
			score, i, total)
	}
	
	average := float64(total) / float64(len(scores))
	fmt.Printf("Final total: %d, Average: %.1f\n", total, average)
	
	fmt.Println()
	
	// Example 2: Find maximum value
	temperatures := []float64{72.5, 75.0, 68.3, 71.2, 74.8}
	
	maxTemp := temperatures[0] // Start with first temperature
	maxIndex := 0
	
	fmt.Println("Finding maximum temperature:")
	for index, temp := range temperatures {
		fmt.Printf("Day %d: %.1f°F", index+1, temp)
		
		if temp > maxTemp {
			maxTemp = temp
			maxIndex = index
			fmt.Printf(" <- New maximum!")
		}
		fmt.Println()
	}
	
	fmt.Printf("Highest temperature: %.1f°F on day %d\n", maxTemp, maxIndex+1)
	
	fmt.Println()
	
	// Example 3: Count occurrences in map
	votes := map[string]int{
		"Pizza":   15,
		"Burger":  12,
		"Salad":   8,
		"Pasta":   10,
	}
	
	fmt.Println("Vote counting:")
	totalVotes := 0
	winner := ""
	maxVotes := 0
	
	for food, count := range votes {
		totalVotes += count
		fmt.Printf("%s: %d votes", food, count)
		
		if count > maxVotes {
			maxVotes = count
			winner = food
			fmt.Printf(" <- Current leader!")
		}
		fmt.Println()
	}
	
	fmt.Printf("\nElection Results:\n")
	fmt.Printf("Winner: %s with %d votes\n", winner, maxVotes)
	fmt.Printf("Total votes cast: %d\n", totalVotes)
	
	// Calculate percentages
	fmt.Println("\nVote percentages:")
	for food, count := range votes {
		percentage := float64(count) / float64(totalVotes) * 100
		fmt.Printf("%s: %.1f%%\n", food, percentage)
	}
}
