// Package declaration - this line tells Go which package this file belongs to
package main

// Import statement - this brings in the fmt package for input/output
import "fmt"

// SINGLE LINE COMMENTS
// Use // for single line comments
// Everything after // on that line is ignored by Go
// You can use multiple // lines for longer explanations

/*
MULTI-LINE COMMENTS
Use /* to start and */ to end multi-line comments
Everything between these symbols is ignored
This is useful for longer explanations
or temporarily disabling large blocks of code
*/

// Main function - the entry point of our program
func main() {
	// Variable declarations with explanatory comments
	name := "Alice"        // Store the user's name
	age := 25             // Store the user's age in years
	salary := 50000.0     // Annual salary in dollars
	
	// Display user information
	fmt.Println("=== User Profile ===")
	fmt.Printf("Name: %s\n", name)     // Print the name
	fmt.Printf("Age: %d\n", age)       // Print the age
	fmt.Printf("Salary: $%.2f\n", salary) // Print salary with 2 decimal places
	
	// Calculate monthly salary
	// Divide annual salary by 12 months
	monthlySalary := salary / 12
	fmt.Printf("Monthly Salary: $%.2f\n", monthlySalary)
	
	// TODO: Add tax calculation later
	// FIXME: Need to handle negative salary values
	// NOTE: This assumes a standard 12-month year
	
	/*
	Future enhancements:
	1. Add input validation
	2. Support different currencies
	3. Include tax calculations
	4. Add bonus calculations
	*/
}
