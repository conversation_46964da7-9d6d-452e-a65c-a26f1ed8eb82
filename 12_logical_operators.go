// Package declaration
package main

// Import fmt for printing
import "fmt"

// Main function
func main() {
	// LOGICAL OPERATORS
	// These operators work with true/false values and combine conditions
	
	// Sample data for a user
	age := 25
	hasLicense := true
	hasInsurance := true
	hasGoodRecord := false
	isStudent := true
	income := 50000
	
	// AND operator (&&)
	// Returns true only if BOTH conditions are true
	canRentCar := age >= 21 && hasLicense
	fmt.Println("Can rent car (age >= 21 AND has license):", canRentCar)
	
	// OR operator (||)
	// Returns true if AT LEAST ONE condition is true
	getsDiscount := age < 25 || isStudent
	fmt.Println("Gets discount (age < 25 OR is student):", getsDiscount)
	
	// NOT operator (!)
	// Flips true to false, and false to true
	isNotStudent := !isStudent
	fmt.Println("Is not a student:", isNotStudent)
	
	fmt.Println()
	
	// Complex logical expressions
	// You can combine multiple logical operators
	
	// Car insurance eligibility
	// Must have license AND insurance AND (good record OR high income)
	insuranceEligible := hasLicense && hasInsurance && (hasGoodRecord || income > 40000)
	fmt.Println("=== Insurance Eligibility ===")
	fmt.Println("Has license:", hasLicense)
	fmt.Println("Has insurance:", hasInsurance)
	fmt.Println("Has good record:", hasGoodRecord)
	fmt.Println("Income > $40,000:", income > 40000)
	fmt.Println("Eligible for car insurance:", insuranceEligible)
	
	fmt.Println()
	
	// Online shopping example
	orderAmount := 75.0
	isPremiumMember := true
	hasPromoCode := false
	
	// Free shipping conditions: (order > $50) OR (premium member AND has promo code)
	freeShipping := orderAmount > 50.0 || (isPremiumMember && hasPromoCode)
	fmt.Println("=== Shipping Calculator ===")
	fmt.Println("Order amount: $", orderAmount)
	fmt.Println("Is premium member:", isPremiumMember)
	fmt.Println("Has promo code:", hasPromoCode)
	fmt.Println("Qualifies for free shipping:", freeShipping)
	
	fmt.Println()
	
	// Login system example
	username := "admin"
	password := "secret123"
	isAccountActive := true
	failedAttempts := 2
	maxAttempts := 3
	
	// Can login: correct credentials AND account active AND not too many failed attempts
	canLogin := username == "admin" && password == "secret123" && isAccountActive && failedAttempts < maxAttempts
	fmt.Println("=== Login System ===")
	fmt.Println("Username correct:", username == "admin")
	fmt.Println("Password correct:", password == "secret123")
	fmt.Println("Account active:", isAccountActive)
	fmt.Println("Under attempt limit:", failedAttempts < maxAttempts)
	fmt.Println("Can login:", canLogin)
}
