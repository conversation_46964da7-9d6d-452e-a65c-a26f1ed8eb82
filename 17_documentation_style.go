// Package main demonstrates proper Go documentation style
// This program calculates compound interest for investments
package main

import "fmt"

// Constants for the investment calculator
// These values represent common investment parameters
const (
	DefaultRate = 0.05  // 5% annual interest rate
	MinAmount   = 100.0 // Minimum investment amount in dollars
	MaxYears    = 50    // Maximum investment period in years
)

// main function demonstrates compound interest calculation
// This is the entry point of the program
func main() {
	// Investment parameters
	// These variables store the user's investment details
	principal := 1000.0 // Initial investment amount (principal)
	rate := 0.07        // Annual interest rate (7%)
	years := 10         // Investment period in years
	
	// Display investment details
	// Show the user what parameters we're using for calculation
	fmt.Println("=== COMPOUND INTEREST CALCULATOR ===")
	fmt.Printf("Principal Amount: $%.2f\n", principal)
	fmt.Printf("Annual Interest Rate: %.1f%%\n", rate*100)
	fmt.Printf("Investment Period: %d years\n", years)
	fmt.Println()
	
	// Calculate compound interest
	// Formula: A = P(1 + r)^t
	// Where: A = final amount, P = principal, r = rate, t = time
	amount := principal
	
	// Loop through each year to show growth
	// This demonstrates how the investment grows year by year
	fmt.Println("Year-by-Year Growth:")
	fmt.Printf("%-4s %-12s %-12s\n", "Year", "Amount", "Interest")
	fmt.Println("---- ------------ ------------")
	
	for year := 1; year <= years; year++ {
		// Calculate interest earned this year
		// Interest = current amount × interest rate
		yearlyInterest := amount * rate
		
		// Add interest to the total amount
		// This is compounding - earning interest on interest
		amount += yearlyInterest
		
		// Display this year's results
		// Show year, total amount, and interest earned
		fmt.Printf("%-4d $%-11.2f $%-11.2f\n", year, amount, yearlyInterest)
	}
	
	// Calculate total interest earned
	// Total interest = final amount - original principal
	totalInterest := amount - principal
	
	// Display final results
	// Summary of the investment performance
	fmt.Println()
	fmt.Println("=== INVESTMENT SUMMARY ===")
	fmt.Printf("Initial Investment: $%.2f\n", principal)
	fmt.Printf("Final Amount: $%.2f\n", amount)
	fmt.Printf("Total Interest Earned: $%.2f\n", totalInterest)
	fmt.Printf("Total Return: %.1f%%\n", (totalInterest/principal)*100)
}
