// Package declaration
package main

// Import fmt for input/output
import "fmt"

// PAYMENT SYSTEM USING INTERFACES
// Demonstrates real-world interface usage

// PaymentProcessor interface defines what all payment methods must do
type PaymentProcessor interface {
	ProcessPayment(amount float64) (bool, string)
	GetProviderName() string
	ValidatePayment(amount float64) (bool, string)
}

// Refundable interface for payment methods that support refunds
type Refundable interface {
	ProcessRefund(amount float64) (bool, string)
}

// PAYMENT METHOD IMPLEMENTATIONS

// CreditCard struct
type CreditCard struct {
	CardNumber string
	HolderName string
	ExpiryDate string
	CVV        string
	Balance    float64
}

// PayPal struct
type PayPal struct {
	Email   string
	Balance float64
}

// BankTransfer struct
type BankTransfer struct {
	AccountNumber string
	RoutingNumber string
	BankName      string
	Balance       float64
}

// CryptoCurrency struct
type CryptoCurrency struct {
	WalletAddress string
	CoinType      string
	Balance       float64
}

// IMPLEMENTING PaymentProcessor INTERFACE

// CreditCard implements PaymentProcessor
func (cc CreditCard) ProcessPayment(amount float64) (bool, string) {
	if amount <= 0 {
		return false, "Invalid payment amount"
	}
	
	if cc.Balance < amount {
		return false, "Insufficient credit limit"
	}
	
	// Simulate payment processing
	return true, fmt.Sprintf("Credit card payment of $%.2f processed successfully", amount)
}

func (cc CreditCard) GetProviderName() string {
	return "Credit Card"
}

func (cc CreditCard) ValidatePayment(amount float64) (bool, string) {
	if amount <= 0 {
		return false, "Amount must be positive"
	}
	if amount > 10000 {
		return false, "Amount exceeds daily limit"
	}
	return true, "Payment validation successful"
}

// PayPal implements PaymentProcessor
func (pp PayPal) ProcessPayment(amount float64) (bool, string) {
	if amount <= 0 {
		return false, "Invalid payment amount"
	}
	
	if pp.Balance < amount {
		return false, "Insufficient PayPal balance"
	}
	
	return true, fmt.Sprintf("PayPal payment of $%.2f processed successfully", amount)
}

func (pp PayPal) GetProviderName() string {
	return "PayPal"
}

func (pp PayPal) ValidatePayment(amount float64) (bool, string) {
	if amount <= 0 {
		return false, "Amount must be positive"
	}
	if amount > 5000 {
		return false, "Amount exceeds PayPal transaction limit"
	}
	return true, "PayPal validation successful"
}

// BankTransfer implements PaymentProcessor
func (bt BankTransfer) ProcessPayment(amount float64) (bool, string) {
	if amount <= 0 {
		return false, "Invalid payment amount"
	}
	
	if bt.Balance < amount {
		return false, "Insufficient bank account balance"
	}
	
	return true, fmt.Sprintf("Bank transfer of $%.2f processed successfully", amount)
}

func (bt BankTransfer) GetProviderName() string {
	return fmt.Sprintf("Bank Transfer (%s)", bt.BankName)
}

func (bt BankTransfer) ValidatePayment(amount float64) (bool, string) {
	if amount <= 0 {
		return false, "Amount must be positive"
	}
	if amount > 50000 {
		return false, "Amount exceeds bank transfer limit"
	}
	return true, "Bank transfer validation successful"
}

// CryptoCurrency implements PaymentProcessor
func (crypto CryptoCurrency) ProcessPayment(amount float64) (bool, string) {
	if amount <= 0 {
		return false, "Invalid payment amount"
	}
	
	if crypto.Balance < amount {
		return false, "Insufficient cryptocurrency balance"
	}
	
	return true, fmt.Sprintf("%s payment of $%.2f processed successfully", crypto.CoinType, amount)
}

func (crypto CryptoCurrency) GetProviderName() string {
	return fmt.Sprintf("Cryptocurrency (%s)", crypto.CoinType)
}

func (crypto CryptoCurrency) ValidatePayment(amount float64) (bool, string) {
	if amount <= 0 {
		return false, "Amount must be positive"
	}
	// Crypto has higher limits but more volatility risk
	if amount > 100000 {
		return false, "Amount exceeds cryptocurrency transaction limit"
	}
	return true, "Cryptocurrency validation successful"
}

// IMPLEMENTING Refundable INTERFACE

// CreditCard implements Refundable
func (cc CreditCard) ProcessRefund(amount float64) (bool, string) {
	if amount <= 0 {
		return false, "Invalid refund amount"
	}
	return true, fmt.Sprintf("Credit card refund of $%.2f processed successfully", amount)
}

// PayPal implements Refundable
func (pp PayPal) ProcessRefund(amount float64) (bool, string) {
	if amount <= 0 {
		return false, "Invalid refund amount"
	}
	return true, fmt.Sprintf("PayPal refund of $%.2f processed successfully", amount)
}

// Note: BankTransfer and CryptoCurrency don't implement Refundable
// (simulating that some payment methods don't support refunds)

// BUSINESS LOGIC FUNCTIONS

// Function to process any payment using interface
func processPayment(processor PaymentProcessor, amount float64) {
	fmt.Printf("\n--- Processing Payment with %s ---\n", processor.GetProviderName())
	
	// Validate payment first
	valid, validationMsg := processor.ValidatePayment(amount)
	if !valid {
		fmt.Printf("❌ Validation failed: %s\n", validationMsg)
		return
	}
	
	fmt.Printf("✅ Validation: %s\n", validationMsg)
	
	// Process payment
	success, message := processor.ProcessPayment(amount)
	if success {
		fmt.Printf("✅ %s\n", message)
	} else {
		fmt.Printf("❌ Payment failed: %s\n", message)
	}
}

// Function to process refund if supported
func processRefund(processor PaymentProcessor, amount float64) {
	fmt.Printf("\n--- Processing Refund with %s ---\n", processor.GetProviderName())
	
	// Check if the processor supports refunds
	if refundable, ok := processor.(Refundable); ok {
		success, message := refundable.ProcessRefund(amount)
		if success {
			fmt.Printf("✅ %s\n", message)
		} else {
			fmt.Printf("❌ Refund failed: %s\n", message)
		}
	} else {
		fmt.Printf("❌ %s does not support refunds\n", processor.GetProviderName())
	}
}

// Function to get payment recommendations
func recommendPaymentMethod(amount float64, processors []PaymentProcessor) {
	fmt.Printf("\n--- Payment Recommendations for $%.2f ---\n", amount)
	
	var validProcessors []PaymentProcessor
	
	for _, processor := range processors {
		valid, _ := processor.ValidatePayment(amount)
		if valid {
			validProcessors = append(validProcessors, processor)
		}
	}
	
	if len(validProcessors) == 0 {
		fmt.Println("❌ No payment methods available for this amount")
		return
	}
	
	fmt.Println("✅ Available payment methods:")
	for i, processor := range validProcessors {
		fmt.Printf("%d. %s\n", i+1, processor.GetProviderName())
	}
}

// Main function
func main() {
	fmt.Println("=== PAYMENT PROCESSING SYSTEM ===")
	fmt.Println("Demonstrating interfaces in a real-world scenario")
	fmt.Println()
	
	// CREATE PAYMENT METHODS
	creditCard := CreditCard{
		CardNumber: "**** **** **** 1234",
		HolderName: "John Doe",
		ExpiryDate: "12/25",
		CVV:        "123",
		Balance:    5000.00,
	}
	
	paypal := PayPal{
		Email:   "<EMAIL>",
		Balance: 2000.00,
	}
	
	bankTransfer := BankTransfer{
		AccountNumber: "****5678",
		RoutingNumber: "*********",
		BankName:      "First National Bank",
		Balance:       15000.00,
	}
	
	crypto := CryptoCurrency{
		WalletAddress: "**********************************",
		CoinType:      "Bitcoin",
		Balance:       50000.00,
	}
	
	// STORE ALL PROCESSORS IN SLICE
	processors := []PaymentProcessor{creditCard, paypal, bankTransfer, crypto}
	
	// TEST DIFFERENT PAYMENT AMOUNTS
	testAmounts := []float64{100.00, 2500.00, 7500.00, 25000.00}
	
	for _, amount := range testAmounts {
		fmt.Printf("\n" + "="*50)
		fmt.Printf("\nTesting payment amount: $%.2f\n", amount)
		fmt.Printf("="*50)
		
		// Get recommendations
		recommendPaymentMethod(amount, processors)
		
		// Try processing with each method
		for _, processor := range processors {
			processPayment(processor, amount)
		}
	}
	
	// TEST REFUND FUNCTIONALITY
	fmt.Printf("\n" + "="*50)
	fmt.Printf("\nTesting Refund Functionality\n")
	fmt.Printf("="*50)
	
	refundAmount := 150.00
	
	for _, processor := range processors {
		processRefund(processor, refundAmount)
	}
	
	// DEMONSTRATE POLYMORPHISM
	fmt.Printf("\n" + "="*50)
	fmt.Printf("\nPolymorphism Demonstration\n")
	fmt.Printf("="*50)
	
	// Function that works with any PaymentProcessor
	processMultiplePayments := func(amount float64, processors []PaymentProcessor) {
		fmt.Printf("\nProcessing $%.2f payment with all available methods:\n", amount)
		
		successCount := 0
		for _, processor := range processors {
			valid, _ := processor.ValidatePayment(amount)
			if valid {
				success, _ := processor.ProcessPayment(amount)
				if success {
					successCount++
					fmt.Printf("✅ %s: Success\n", processor.GetProviderName())
				} else {
					fmt.Printf("❌ %s: Failed\n", processor.GetProviderName())
				}
			} else {
				fmt.Printf("⚠️ %s: Not available for this amount\n", processor.GetProviderName())
			}
		}
		
		fmt.Printf("\nSummary: %d out of %d payment methods succeeded\n", 
			successCount, len(processors))
	}
	
	processMultiplePayments(1000.00, processors)
	
	fmt.Println()
	fmt.Println("Payment system demonstration complete! 💳")
}
