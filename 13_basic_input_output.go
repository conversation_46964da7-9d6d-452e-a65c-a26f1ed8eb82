// Package declaration
package main

// Import fmt for input/output operations
import "fmt"

// Main function
func main() {
	// OUTPUT - Different ways to print information
	
	// fmt.Print - prints without adding a new line
	fmt.Print("Hello ")
	fmt.Print("World!")
	fmt.Print(" This is all on one line.\n") // \n adds a new line manually
	
	// fmt.Println - prints and adds a new line automatically
	fmt.Println("This is on a new line")
	fmt.Println("And this is on another line")
	
	// fmt.Printf - prints with formatting (like placeholders)
	name := "Alice"
	age := 25
	height := 5.6
	
	// %s is placeholder for string, %d for integer, %f for float
	fmt.Printf("Name: %s, Age: %d, Height: %.1f feet\n", name, age, height)
	
	fmt.Println()
	
	// INPUT - Getting information from the user
	var userName string
	var userAge int
	
	// Ask user for their name
	fmt.Print("Enter your name: ")
	// fmt.Scan reads input from user and stores it in the variable
	fmt.Scan(&userName) // & means "address of" - we'll learn this later
	
	// Ask user for their age
	fmt.Print("Enter your age: ")
	fmt.Scan(&userAge)
	
	// Display the information we collected
	fmt.Println()
	fmt.Println("=== User Information ===")
	fmt.Printf("Hello %s! You are %d years old.\n", userName, userAge)
	
	// Calculate and display birth year (approximately)
	currentYear := 2024
	birthYear := currentYear - userAge
	fmt.Printf("You were probably born in %d.\n", birthYear)
}
