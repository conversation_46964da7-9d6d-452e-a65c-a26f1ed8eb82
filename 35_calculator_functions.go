// Package declaration
package main

// Import fmt for input/output
import "fmt"

// CALCULATOR FUNCTIONS
// Demonstrates organizing code with functions

// Basic arithmetic functions
func add(a, b float64) float64 {
	return a + b
}

func subtract(a, b float64) float64 {
	return a - b
}

func multiply(a, b float64) float64 {
	return a * b
}

func divide(a, b float64) (float64, bool) {
	// Return result and success flag
	if b == 0 {
		return 0, false // Division by zero
	}
	return a / b, true
}

// Advanced math functions
func power(base, exponent float64) float64 {
	// Simple power function (for positive integer exponents)
	if exponent == 0 {
		return 1
	}
	
	result := 1.0
	for i := 0; i < int(exponent); i++ {
		result *= base
	}
	return result
}

func squareRoot(number float64) (float64, bool) {
	// Simple square root using <PERSON>'s method
	if number < 0 {
		return 0, false // Can't take square root of negative number
	}
	
	if number == 0 {
		return 0, true
	}
	
	// <PERSON>'s method for square root
	guess := number / 2
	for i := 0; i < 10; i++ { // 10 iterations should be enough
		guess = (guess + number/guess) / 2
	}
	
	return guess, true
}

// Utility functions
func isPositive(number float64) bool {
	return number > 0
}

func isNegative(number float64) bool {
	return number < 0
}

func absolute(number float64) float64 {
	if number < 0 {
		return -number
	}
	return number
}

// Function to display menu
func displayMenu() {
	fmt.Println("\n=== CALCULATOR MENU ===")
	fmt.Println("1. Addition")
	fmt.Println("2. Subtraction")
	fmt.Println("3. Multiplication")
	fmt.Println("4. Division")
	fmt.Println("5. Power")
	fmt.Println("6. Square Root")
	fmt.Println("7. Absolute Value")
	fmt.Println("0. Exit")
	fmt.Print("Choose operation: ")
}

// Function to get two numbers from user (simulated)
func getTwoNumbers() (float64, float64) {
	// In a real program, you'd use fmt.Scan to get user input
	// For this example, we'll simulate with predefined values
	return 12.5, 4.0
}

// Function to get one number from user (simulated)
func getOneNumber() float64 {
	// Simulated user input
	return 16.0
}

// Main function
func main() {
	fmt.Println("=== ADVANCED CALCULATOR ===")
	fmt.Println("Demonstrating functions in a real application")
	
	// Simulate calculator operations
	choice := 1 // Simulate user choosing addition
	
	for choice != 0 {
		displayMenu()
		
		// Simulate user input (in real program, use fmt.Scan)
		fmt.Printf("%d\n", choice) // Show what choice was "selected"
		
		switch choice {
		case 1: // Addition
			num1, num2 := getTwoNumbers()
			result := add(num1, num2)
			fmt.Printf("%.2f + %.2f = %.2f\n", num1, num2, result)
			
		case 2: // Subtraction
			num1, num2 := getTwoNumbers()
			result := subtract(num1, num2)
			fmt.Printf("%.2f - %.2f = %.2f\n", num1, num2, result)
			
		case 3: // Multiplication
			num1, num2 := getTwoNumbers()
			result := multiply(num1, num2)
			fmt.Printf("%.2f × %.2f = %.2f\n", num1, num2, result)
			
		case 4: // Division
			num1, num2 := getTwoNumbers()
			result, success := divide(num1, num2)
			if success {
				fmt.Printf("%.2f ÷ %.2f = %.2f\n", num1, num2, result)
			} else {
				fmt.Println("Error: Division by zero!")
			}
			
		case 5: // Power
			base, exponent := getTwoNumbers()
			result := power(base, exponent)
			fmt.Printf("%.2f ^ %.0f = %.2f\n", base, exponent, result)
			
		case 6: // Square Root
			number := getOneNumber()
			result, success := squareRoot(number)
			if success {
				fmt.Printf("√%.2f = %.2f\n", number, result)
			} else {
				fmt.Println("Error: Cannot take square root of negative number!")
			}
			
		case 7: // Absolute Value
			number := getOneNumber()
			result := absolute(number)
			fmt.Printf("|%.2f| = %.2f\n", number, result)
			
			// Show additional info
			if isPositive(number) {
				fmt.Println("The number is positive")
			} else if isNegative(number) {
				fmt.Println("The number is negative")
			} else {
				fmt.Println("The number is zero")
			}
			
		case 0: // Exit
			fmt.Println("Thank you for using the calculator!")
			
		default:
			fmt.Println("Invalid choice! Please try again.")
		}
		
		// For demonstration, we'll cycle through different operations
		choice++
		if choice > 7 {
			choice = 0 // Exit after showing all operations
		}
		
		fmt.Println() // Add spacing between operations
	}
	
	// DEMONSTRATION OF FUNCTION COMPOSITION
	fmt.Println("=== Function Composition Examples ===")
	
	// Using functions together
	x := 3.0
	y := 4.0
	
	// Calculate hypotenuse using multiple functions
	xSquared := power(x, 2)
	ySquared := power(y, 2)
	sumOfSquares := add(xSquared, ySquared)
	hypotenuse, _ := squareRoot(sumOfSquares)
	
	fmt.Printf("Right triangle with sides %.1f and %.1f:\n", x, y)
	fmt.Printf("%.1f² = %.1f\n", x, xSquared)
	fmt.Printf("%.1f² = %.1f\n", y, ySquared)
	fmt.Printf("%.1f + %.1f = %.1f\n", xSquared, ySquared, sumOfSquares)
	fmt.Printf("√%.1f = %.2f (hypotenuse)\n", sumOfSquares, hypotenuse)
	
	fmt.Println()
	
	// More function combinations
	numbers := []float64{-5.5, 3.2, -8.1, 12.7, -2.3}
	fmt.Println("Processing array of numbers:")
	
	sum := 0.0
	for _, num := range numbers {
		absValue := absolute(num)
		sum = add(sum, absValue)
		
		fmt.Printf("Number: %6.1f, Absolute: %6.1f, Running sum: %6.1f\n", 
			num, absValue, sum)
	}
	
	average := divide(sum, float64(len(numbers)))
	fmt.Printf("Average of absolute values: %.2f\n", average)
}
