// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// SHOPPING CART SYSTEM
	// Demonstrates practical use of slices in e-commerce
	
	// Initialize empty shopping cart
	var cart []string
	var prices []float64
	var quantities []int
	
	fmt.Println("=== ONLINE SHOPPING CART ===")
	fmt.Println("Starting with empty cart...")
	fmt.Printf("Cart items: %d\n", len(cart))
	fmt.Println()
	
	// ADD ITEMS TO CART
	fmt.Println("=== Adding Items ===")
	
	// Add first item
	cart = append(cart, "Laptop")
	prices = append(prices, 999.99)
	quantities = append(quantities, 1)
	fmt.Println("Added: Laptop - $999.99 x 1")
	
	// Add more items
	newItems := []string{"Mouse", "Keyboard", "Monitor"}
	newPrices := []float64{29.99, 79.99, 299.99}
	newQuantities := []int{2, 1, 1}
	
	// Add multiple items at once
	cart = append(cart, newItems...)
	prices = append(prices, newPrices...)
	quantities = append(quantities, newQuantities...)
	
	fmt.Println("Added: Mouse - $29.99 x 2")
	fmt.Println("Added: Keyboard - $79.99 x 1")
	fmt.Println("Added: Monitor - $299.99 x 1")
	fmt.Println()
	
	// DISPLAY CART CONTENTS
	fmt.Println("=== Current Cart ===")
	fmt.Printf("%-15s %-10s %-5s %-10s\n", "Item", "Price", "Qty", "Subtotal")
	fmt.Println("------------------------------------------------")
	
	var totalAmount float64
	
	for i := 0; i < len(cart); i++ {
		subtotal := prices[i] * float64(quantities[i])
		totalAmount += subtotal
		
		fmt.Printf("%-15s $%-9.2f %-5d $%-9.2f\n", 
			cart[i], prices[i], quantities[i], subtotal)
	}
	
	fmt.Println("------------------------------------------------")
	fmt.Printf("Total: $%.2f\n", totalAmount)
	fmt.Printf("Items in cart: %d\n", len(cart))
	fmt.Println()
	
	// MODIFY CART - Update quantity
	fmt.Println("=== Updating Quantities ===")
	// Customer wants 3 mice instead of 2
	mouseIndex := 1 // Mouse is at index 1
	oldQuantity := quantities[mouseIndex]
	quantities[mouseIndex] = 3
	
	fmt.Printf("Updated %s quantity from %d to %d\n", 
		cart[mouseIndex], oldQuantity, quantities[mouseIndex])
	
	// Recalculate total
	totalAmount = 0
	for i := 0; i < len(cart); i++ {
		totalAmount += prices[i] * float64(quantities[i])
	}
	fmt.Printf("New total: $%.2f\n", totalAmount)
	fmt.Println()
	
	// REMOVE ITEM FROM CART
	fmt.Println("=== Removing Items ===")
	fmt.Printf("Removing %s from cart...\n", cart[2]) // Remove keyboard
	
	// Remove item at index 2 (keyboard)
	indexToRemove := 2
	cart = append(cart[:indexToRemove], cart[indexToRemove+1:]...)
	prices = append(prices[:indexToRemove], prices[indexToRemove+1:]...)
	quantities = append(quantities[:indexToRemove], quantities[indexToRemove+1:]...)
	
	fmt.Println("Keyboard removed from cart")
	fmt.Println()
	
	// FINAL CART DISPLAY
	fmt.Println("=== Final Cart ===")
	fmt.Printf("%-15s %-10s %-5s %-10s\n", "Item", "Price", "Qty", "Subtotal")
	fmt.Println("------------------------------------------------")
	
	totalAmount = 0
	totalItems := 0
	
	for i := 0; i < len(cart); i++ {
		subtotal := prices[i] * float64(quantities[i])
		totalAmount += subtotal
		totalItems += quantities[i]
		
		fmt.Printf("%-15s $%-9.2f %-5d $%-9.2f\n", 
			cart[i], prices[i], quantities[i], subtotal)
	}
	
	fmt.Println("------------------------------------------------")
	fmt.Printf("Total Amount: $%.2f\n", totalAmount)
	fmt.Printf("Total Items: %d\n", totalItems)
	fmt.Printf("Unique Products: %d\n", len(cart))
	
	// APPLY DISCOUNTS
	fmt.Println()
	fmt.Println("=== Applying Discounts ===")
	
	var discount float64
	var discountReason string
	
	// Check for bulk discount
	if totalItems >= 5 {
		discount = totalAmount * 0.10 // 10% bulk discount
		discountReason = "Bulk purchase (5+ items)"
	} else if totalAmount >= 500 {
		discount = totalAmount * 0.05 // 5% high value discount
		discountReason = "High value purchase ($500+)"
	}
	
	if discount > 0 {
		finalAmount := totalAmount - discount
		fmt.Printf("Discount applied: %s\n", discountReason)
		fmt.Printf("Discount amount: -$%.2f\n", discount)
		fmt.Printf("Final total: $%.2f\n", finalAmount)
	} else {
		fmt.Println("No discounts applicable")
		fmt.Printf("Final total: $%.2f\n", totalAmount)
	}
	
	fmt.Println()
	fmt.Println("Thank you for shopping with us! 🛒")
}
