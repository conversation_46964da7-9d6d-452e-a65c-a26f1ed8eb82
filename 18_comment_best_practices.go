// Package main demonstrates comment best practices in Go
// This program shows good and bad commenting examples
package main

import "fmt"

// GOOD COMMENTING PRACTICES

// calculateTax computes the tax amount based on income and tax rate
// This function handles the business logic for tax calculation
func calculateTax(income, rate float64) float64 {
	// Apply standard tax calculation formula
	// Tax = Income × Tax Rate
	return income * rate
}

// BAD COMMENTING PRACTICES (examples of what NOT to do)

// This function adds two numbers (OBVIOUS - don't comment obvious things)
func add(a, b int) int {
	return a + b // Return the sum (REDUNDANT)
}

// Main function
func main() {
	// GOOD: Explain WHY, not WHAT
	// We use 0.22 because this represents the current federal tax rate for this bracket
	taxRate := 0.22
	
	// BAD: Obvious comment
	// income := 50000 // Set income to 50000
	
	// GOOD: Explain complex business logic
	income := 50000.0
	
	// Calculate federal tax using current tax brackets
	// Note: This is a simplified calculation - real tax calculation
	// would involve multiple brackets and deductions
	federalTax := calculateTax(income, taxRate)
	
	// GOOD: Explain assumptions or limitations
	// State tax calculation assumes California rate (9.3%)
	// TODO: Make this configurable for different states
	stateTaxRate := 0.093
	stateTax := calculateTax(income, stateTaxRate)
	
	// Calculate total tax burden
	totalTax := federalTax + stateTax
	afterTaxIncome := income - totalTax
	
	// Display results with clear formatting
	fmt.Println("=== TAX CALCULATION SUMMARY ===")
	fmt.Printf("Gross Income:     $%8.2f\n", income)
	fmt.Printf("Federal Tax:      $%8.2f\n", federalTax)
	fmt.Printf("State Tax:        $%8.2f\n", stateTax)
	fmt.Printf("Total Tax:        $%8.2f\n", totalTax)
	fmt.Printf("After-Tax Income: $%8.2f\n", afterTaxIncome)
	
	// GOOD: Explain calculations that might not be obvious
	// Effective tax rate = total tax / gross income
	effectiveRate := (totalTax / income) * 100
	fmt.Printf("Effective Tax Rate: %.1f%%\n", effectiveRate)
	
	/*
	DOCUMENTATION BLOCK
	
	This program demonstrates tax calculation for educational purposes.
	
	Assumptions:
	- Single tax bracket (simplified)
	- California state tax rate
	- No deductions or credits applied
	- Current year tax rates
	
	Future improvements:
	- Support multiple tax brackets
	- Add deduction calculations
	- Make state tax configurable
	- Include FICA taxes
	*/
}
