// Package calculator provides basic mathematical operations
// This file contains the functions we want to test
package calculator

import (
	"errors"
	"math"
)

// Add returns the sum of two integers
func Add(a, b int) int {
	return a + b
}

// Subtract returns the difference of two integers
func Subtract(a, b int) int {
	return a - b
}

// Multiply returns the product of two integers
func Multiply(a, b int) int {
	return a * b
}

// Divide returns the quotient of two integers and an error
// Returns an error if the divisor is zero
func Divide(a, b int) (float64, error) {
	if b == 0 {
		return 0, errors.New("division by zero")
	}
	return float64(a) / float64(b), nil
}

// Power returns a raised to the power of b
func Power(a, b int) float64 {
	return math.Pow(float64(a), float64(b))
}

// IsEven checks if a number is even
func IsEven(n int) bool {
	return n%2 == 0
}

// Factorial calculates the factorial of a non-negative integer
func Factorial(n int) (int, error) {
	if n < 0 {
		return 0, errors.New("factorial is not defined for negative numbers")
	}
	
	if n == 0 || n == 1 {
		return 1, nil
	}
	
	result := 1
	for i := 2; i <= n; i++ {
		result *= i
	}
	
	return result, nil
}

// Max returns the larger of two integers
func Max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// Min returns the smaller of two integers
func Min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// Average calculates the average of a slice of integers
func Average(numbers []int) (float64, error) {
	if len(numbers) == 0 {
		return 0, errors.New("cannot calculate average of empty slice")
	}
	
	sum := 0
	for _, num := range numbers {
		sum += num
	}
	
	return float64(sum) / float64(len(numbers)), nil
}

// IsPrime checks if a number is prime
func IsPrime(n int) bool {
	if n < 2 {
		return false
	}
	
	if n == 2 {
		return true
	}
	
	if n%2 == 0 {
		return false
	}
	
	// Check odd divisors up to sqrt(n)
	for i := 3; i*i <= n; i += 2 {
		if n%i == 0 {
			return false
		}
	}
	
	return true
}

// GCD calculates the Greatest Common Divisor of two integers
func GCD(a, b int) int {
	// Make sure we work with positive numbers
	if a < 0 {
		a = -a
	}
	if b < 0 {
		b = -b
	}
	
	// Euclidean algorithm
	for b != 0 {
		a, b = b, a%b
	}
	
	return a
}
