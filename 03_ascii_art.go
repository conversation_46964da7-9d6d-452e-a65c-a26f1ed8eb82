// Package declaration
package main

// Import fmt for printing
import "fmt"

// Main function
func main() {
	// Print ASCII art of "GO" using text characters
	// Each fmt.Println prints one line of the art
	fmt.Println("  ____   ___  ")
	fmt.Println(" / ___| / _ \\ ")
	fmt.Println("| |  _ | | | |")
	fmt.Println("| |_| || |_| |")
	fmt.Println(" \\____| \\___/ ")
	
	// Print a blank line for spacing
	fmt.Println()
	
	// Print a motivational message
	fmt.Println("🚀 Ready to learn Go programming!")
	
	// Print another blank line
	fmt.Println()
	
	// Print a simple border using repeated characters
	fmt.Println("********************************")
	fmt.Println("*     Welcome to Go World!     *")
	fmt.Println("********************************")
}
