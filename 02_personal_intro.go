// Package declaration - every Go file needs this
package main

// Import the fmt package for printing
import "fmt"

// Main function - the entry point of our program
func main() {
	// Print a personal introduction
	// Each fmt.Println creates a new line after printing
	fmt.Println("=== Personal Introduction ===")
	
	// Print your name (you can change this to your actual name)
	fmt.Println("Name: <PERSON>")
	
	// Print your age
	fmt.Println("Age: 25")
	
	// Print your hobby
	fmt.Println("Hobby: Learning Go programming")
	
	// Print a fun fact
	fmt.Println("Fun fact: I'm learning my first programming language!")
	
	// Print a separator line
	fmt.Println("=============================")
}
