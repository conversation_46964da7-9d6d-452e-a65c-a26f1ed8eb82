// Package declaration
package main

// Import fmt for input/output
import "fmt"

// BASIC MULTIPLE RETURN FUNCTIONS

// Function returning two integers
func getMinMax(numbers []int) (int, int) {
	if len(numbers) == 0 {
		return 0, 0 // Return zeros if empty slice
	}
	
	min := numbers[0]
	max := numbers[0]
	
	for _, num := range numbers {
		if num < min {
			min = num
		}
		if num > max {
			max = num
		}
	}
	
	return min, max
}

// Function returning result and success status
func safeDivide(a, b float64) (float64, bool) {
	if b == 0 {
		return 0.0, false // Return 0 and failure flag
	}
	return a / b, true // Return result and success flag
}

// Function returning multiple different types
func analyzeText(text string) (int, int, int) {
	charCount := len(text)
	wordCount := 0
	spaceCount := 0
	
	// Count words and spaces
	inWord := false
	for _, char := range text {
		if char == ' ' {
			spaceCount++
			inWord = false
		} else {
			if !inWord {
				wordCount++
				inWord = true
			}
		}
	}
	
	return charCount, wordCount, spaceCount
}

// Function with named return values
func calculateStats(numbers []int) (sum int, average float64, count int) {
	// Named return values are automatically initialized to zero values
	count = len(numbers)
	
	if count == 0 {
		return // Returns sum=0, average=0.0, count=0
	}
	
	// Calculate sum
	for _, num := range numbers {
		sum += num
	}
	
	// Calculate average
	average = float64(sum) / float64(count)
	
	return // Naked return - returns the named values
}

// Function returning a result and error message
func validateAge(age int) (bool, string) {
	if age < 0 {
		return false, "Age cannot be negative"
	}
	if age > 150 {
		return false, "Age seems unrealistic"
	}
	if age < 18 {
		return false, "Must be 18 or older"
	}
	return true, "Age is valid"
}

// Function returning coordinates
func getCoordinates(location string) (float64, float64, bool) {
	// Simulate a location database
	locations := map[string][2]float64{
		"New York":  {40.7128, -74.0060},
		"London":    {51.5074, -0.1278},
		"Tokyo":     {35.6762, 139.6503},
		"Sydney":    {-33.8688, 151.2093},
	}
	
	if coords, exists := locations[location]; exists {
		return coords[0], coords[1], true // latitude, longitude, found
	}
	
	return 0.0, 0.0, false // not found
}

// Main function
func main() {
	fmt.Println("=== MULTIPLE RETURN VALUES ===")
	fmt.Println()
	
	// BASIC MULTIPLE RETURNS
	fmt.Println("=== Finding Min and Max ===")
	
	numbers := []int{45, 23, 67, 12, 89, 34, 56}
	min, max := getMinMax(numbers)
	
	fmt.Printf("Numbers: %v\n", numbers)
	fmt.Printf("Minimum: %d\n", min)
	fmt.Printf("Maximum: %d\n", max)
	
	fmt.Println()
	
	// RESULT WITH SUCCESS FLAG
	fmt.Println("=== Safe Division ===")
	
	// Test cases for division
	testCases := [][2]float64{
		{10.0, 2.0},
		{15.0, 3.0},
		{7.0, 0.0}, // Division by zero
		{20.0, 4.0},
	}
	
	for _, testCase := range testCases {
		a, b := testCase[0], testCase[1]
		result, success := safeDivide(a, b)
		
		if success {
			fmt.Printf("%.1f ÷ %.1f = %.2f ✅\n", a, b, result)
		} else {
			fmt.Printf("%.1f ÷ %.1f = Error: Division by zero! ❌\n", a, b)
		}
	}
	
	fmt.Println()
	
	// MULTIPLE DIFFERENT TYPES
	fmt.Println("=== Text Analysis ===")
	
	texts := []string{
		"Hello World",
		"Go programming is fun",
		"Multiple return values are powerful",
		"",
	}
	
	for _, text := range texts {
		chars, words, spaces := analyzeText(text)
		
		if text == "" {
			fmt.Printf("Text: (empty string)\n")
		} else {
			fmt.Printf("Text: \"%s\"\n", text)
		}
		fmt.Printf("  Characters: %d, Words: %d, Spaces: %d\n", chars, words, spaces)
		fmt.Println()
	}
	
	// NAMED RETURN VALUES
	fmt.Println("=== Statistics with Named Returns ===")
	
	testNumbers := [][]int{
		{1, 2, 3, 4, 5},
		{10, 20, 30},
		{100},
		{}, // Empty slice
	}
	
	for i, nums := range testNumbers {
		sum, avg, count := calculateStats(nums)
		
		fmt.Printf("Dataset %d: %v\n", i+1, nums)
		fmt.Printf("  Count: %d, Sum: %d, Average: %.2f\n", count, sum, avg)
		fmt.Println()
	}
	
	// VALIDATION WITH ERROR MESSAGES
	fmt.Println("=== Age Validation ===")
	
	ages := []int{25, -5, 200, 16, 30, 0}
	
	for _, age := range ages {
		isValid, message := validateAge(age)
		
		if isValid {
			fmt.Printf("Age %d: ✅ %s\n", age, message)
		} else {
			fmt.Printf("Age %d: ❌ %s\n", age, message)
		}
	}
	
	fmt.Println()
	
	// COORDINATE LOOKUP
	fmt.Println("=== Location Coordinates ===")
	
	cities := []string{"New York", "London", "Tokyo", "Paris", "Sydney"}
	
	for _, city := range cities {
		lat, lng, found := getCoordinates(city)
		
		if found {
			fmt.Printf("%s: %.4f°, %.4f° ✅\n", city, lat, lng)
		} else {
			fmt.Printf("%s: Location not found ❌\n", city)
		}
	}
	
	fmt.Println()
	
	// IGNORING RETURN VALUES
	fmt.Println("=== Ignoring Return Values ===")
	
	// Sometimes you only need some of the return values
	// Use blank identifier (_) to ignore unwanted values
	
	_, maxOnly := getMinMax([]int{1, 5, 3, 9, 2})
	fmt.Printf("Only interested in max: %d\n", maxOnly)
	
	result, _ := safeDivide(20, 4)
	fmt.Printf("Only interested in result: %.2f\n", result)
	
	_, _, spaceCount := analyzeText("Hello World Programming")
	fmt.Printf("Only interested in spaces: %d\n", spaceCount)
	
	fmt.Println()
	
	// CHAINING FUNCTIONS WITH MULTIPLE RETURNS
	fmt.Println("=== Function Chaining ===")
	
	// Use return values from one function as input to another
	testNums := []int{12, 45, 23, 67, 34}
	min, max = getMinMax(testNums)
	
	// Use min and max in another calculation
	range_value := max - min
	sum, average, count := calculateStats(testNums)
	
	fmt.Printf("Numbers: %v\n", testNums)
	fmt.Printf("Range (max - min): %d - %d = %d\n", max, min, range_value)
	fmt.Printf("Statistics: sum=%d, avg=%.2f, count=%d\n", sum, average, count)
}
