// Package declaration
package main

// Import fmt for input/output
import "fmt"

// DEFER EXAMPLES

// Function demonstrating basic defer
func demonstrateDefer() {
	fmt.Println("Function starts")
	
	// defer schedules this to run when function exits
	defer fmt.Println("This runs when function ends (deferred)")
	
	fmt.Println("Function middle")
	fmt.Println("Function about to end")
	
	// When function ends, deferred statement runs
}

// Function showing multiple defers (LIFO - Last In, First Out)
func multipleDefers() {
	fmt.Println("=== Multiple Defers (LIFO order) ===")
	
	defer fmt.Println("First defer (runs last)")
	defer fmt.Println("Second defer (runs second)")
	defer fmt.Println("Third defer (runs first)")
	
	fmt.Println("Function body")
}

// Function simulating file operations with defer
func simulateFileOperations() {
	fmt.Println("=== File Operations Simulation ===")
	
	fmt.Println("Opening file...")
	
	// In real code, you'd defer file.Close() here
	defer fmt.Println("Closing file (cleanup)")
	defer fmt.Println("Saving changes (cleanup)")
	defer fmt.Println("Releasing lock (cleanup)")
	
	fmt.Println("Reading file...")
	fmt.Println("Processing data...")
	fmt.Println("Writing results...")
	
	// All deferred functions run automatically when function exits
}

// PANIC EXAMPLES

// Function that demonstrates panic
func demonstratePanic() {
	fmt.Println("Function starts normally")
	
	// defer still runs even if panic occurs
	defer fmt.Println("Defer: This still runs even with panic!")
	
	fmt.Println("About to panic...")
	
	// panic stops normal execution
	panic("Something went terribly wrong!")
	
	// This line will never execute
	fmt.Println("This will never print")
}

// Function that might panic based on input
func riskyDivision(a, b int) int {
	defer fmt.Printf("Defer: Attempted division %d ÷ %d\n", a, b)
	
	if b == 0 {
		panic("Division by zero!")
	}
	
	return a / b
}

// RECOVER EXAMPLES

// Function that recovers from panic
func safeFunction() {
	// defer with recover to catch panics
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("Recovered from panic: %v\n", r)
		}
	}()
	
	fmt.Println("About to do something risky...")
	
	// This will panic
	panic("Oops, something bad happened!")
	
	// This won't execute
	fmt.Println("This won't print")
}

// Function that safely calls risky operations
func safeDivision(a, b int) (result int, err string) {
	// Set up recovery
	defer func() {
		if r := recover(); r != nil {
			result = 0
			err = fmt.Sprintf("Error: %v", r)
		}
	}()
	
	// This might panic
	result = riskyDivision(a, b)
	err = "Success"
	
	return
}

// Function demonstrating defer with variables
func deferWithVariables() {
	fmt.Println("=== Defer with Variables ===")
	
	x := 10
	
	// defer captures the current value of x
	defer fmt.Printf("Deferred: x = %d (captured at defer time)\n", x)
	
	x = 20
	fmt.Printf("Current: x = %d\n", x)
	
	// This defer captures the new value
	defer fmt.Printf("Deferred: x = %d (captured later)\n", x)
	
	x = 30
	fmt.Printf("Final: x = %d\n", x)
}

// Function demonstrating defer in loops
func deferInLoop() {
	fmt.Println("=== Defer in Loop ===")
	
	for i := 1; i <= 3; i++ {
		// Each defer is scheduled separately
		defer fmt.Printf("Deferred from loop: i = %d\n", i)
		fmt.Printf("Loop iteration: i = %d\n", i)
	}
	
	fmt.Println("Loop finished")
}

// Main function
func main() {
	fmt.Println("=== DEFER, PANIC, RECOVER DEMONSTRATION ===")
	fmt.Println()
	
	// BASIC DEFER
	fmt.Println("=== Basic Defer ===")
	demonstrateDefer()
	fmt.Println()
	
	// MULTIPLE DEFERS
	multipleDefers()
	fmt.Println()
	
	// FILE OPERATIONS SIMULATION
	simulateFileOperations()
	fmt.Println()
	
	// DEFER WITH VARIABLES
	deferWithVariables()
	fmt.Println()
	
	// DEFER IN LOOP
	deferInLoop()
	fmt.Println()
	
	// SAFE RECOVERY
	fmt.Println("=== Safe Recovery ===")
	safeFunction()
	fmt.Println("Program continues after recovery")
	fmt.Println()
	
	// SAFE DIVISION EXAMPLES
	fmt.Println("=== Safe Division Examples ===")
	
	testCases := [][2]int{
		{10, 2},
		{15, 3},
		{8, 0}, // This will cause panic, but we'll recover
		{20, 4},
	}
	
	for _, testCase := range testCases {
		a, b := testCase[0], testCase[1]
		result, err := safeDivision(a, b)
		
		if err == "Success" {
			fmt.Printf("%d ÷ %d = %d ✅\n", a, b, result)
		} else {
			fmt.Printf("%d ÷ %d: %s ❌\n", a, b, err)
		}
	}
	
	fmt.Println()
	
	// DEMONSTRATING PANIC (commented out to prevent program crash)
	fmt.Println("=== Panic Demonstration (Controlled) ===")
	fmt.Println("Note: Uncomment the panic demo to see it in action")
	
	// Uncomment this to see panic in action (will crash the program)
	// demonstratePanic()
	
	// Instead, let's show a controlled panic with recovery
	func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("Caught panic in demo: %v\n", r)
			}
		}()
		
		fmt.Println("About to demonstrate controlled panic...")
		panic("This is a controlled panic for demonstration")
	}()
	
	fmt.Println("Program continues after controlled panic")
	fmt.Println()
	
	// PRACTICAL EXAMPLE: Resource Management
	fmt.Println("=== Practical Resource Management ===")
	
	func() {
		fmt.Println("Acquiring resources...")
		
		// Simulate acquiring multiple resources
		defer fmt.Println("🔓 Released database connection")
		defer fmt.Println("📁 Closed file handle")
		defer fmt.Println("🔒 Released mutex lock")
		
		fmt.Println("✅ All resources acquired")
		fmt.Println("💼 Doing important work...")
		fmt.Println("📊 Processing data...")
		
		// Resources are automatically released in reverse order
	}()
	
	fmt.Println()
	
	// PRACTICAL EXAMPLE: Error Handling with Recovery
	fmt.Println("=== Robust Error Handling ===")
	
	processRequests := func(requests []string) {
		for i, request := range requests {
			func() {
				defer func() {
					if r := recover(); r != nil {
						fmt.Printf("Request %d failed: %v (continuing with next)\n", i+1, r)
					}
				}()
				
				fmt.Printf("Processing request %d: %s\n", i+1, request)
				
				// Simulate some requests failing
				if request == "bad_request" {
					panic("Invalid request format")
				}
				
				fmt.Printf("Request %d completed successfully\n", i+1)
			}()
		}
	}
	
	requests := []string{"good_request", "bad_request", "another_good_request"}
	processRequests(requests)
	
	fmt.Println()
	fmt.Println("Defer, Panic, Recover demonstration complete! 🛡️")
}
