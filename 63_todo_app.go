// CLI Todo Application - A complete example demonstrating multiple Go concepts
package main

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"
)

// TODO ITEM STRUCTURE

// TodoItem represents a single todo item
type TodoItem struct {
	ID          int       `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Completed   bool      `json:"completed"`
	CreatedAt   time.Time `json:"created_at"`
	CompletedAt *time.Time `json:"completed_at,omitempty"`
	Priority    string    `json:"priority"` // "low", "medium", "high"
}

// TodoList manages a collection of todo items
type TodoList struct {
	Items    []TodoItem `json:"items"`
	NextID   int        `json:"next_id"`
	filename string
}

// CONSTRUCTOR AND FILE OPERATIONS

// NewTodoList creates a new TodoList
func NewTodoList(filename string) *TodoList {
	return &TodoList{
		Items:    []TodoItem{},
		NextID:   1,
		filename: filename,
	}
}

// LoadFromFile loads todo items from a JSON file
func (tl *TodoList) LoadFromFile() error {
	// Check if file exists
	if _, err := os.Stat(tl.filename); os.IsNotExist(err) {
		// File doesn't exist, start with empty list
		return nil
	}
	
	// Read file
	data, err := os.ReadFile(tl.filename)
	if err != nil {
		return fmt.Errorf("failed to read file: %w", err)
	}
	
	// Parse JSON
	err = json.Unmarshal(data, tl)
	if err != nil {
		return fmt.Errorf("failed to parse JSON: %w", err)
	}
	
	return nil
}

// SaveToFile saves todo items to a JSON file
func (tl *TodoList) SaveToFile() error {
	// Convert to JSON with indentation
	data, err := json.MarshalIndent(tl, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal JSON: %w", err)
	}
	
	// Write to file
	err = os.WriteFile(tl.filename, data, 0644)
	if err != nil {
		return fmt.Errorf("failed to write file: %w", err)
	}
	
	return nil
}

// TODO OPERATIONS

// AddTodo adds a new todo item
func (tl *TodoList) AddTodo(title, description, priority string) *TodoItem {
	// Validate priority
	if priority != "low" && priority != "medium" && priority != "high" {
		priority = "medium" // Default priority
	}
	
	todo := TodoItem{
		ID:          tl.NextID,
		Title:       title,
		Description: description,
		Completed:   false,
		CreatedAt:   time.Now(),
		Priority:    priority,
	}
	
	tl.Items = append(tl.Items, todo)
	tl.NextID++
	
	return &todo
}

// GetTodoByID finds a todo item by ID
func (tl *TodoList) GetTodoByID(id int) (*TodoItem, error) {
	for i := range tl.Items {
		if tl.Items[i].ID == id {
			return &tl.Items[i], nil
		}
	}
	return nil, fmt.Errorf("todo with ID %d not found", id)
}

// CompleteTodo marks a todo item as completed
func (tl *TodoList) CompleteTodo(id int) error {
	for i := range tl.Items {
		if tl.Items[i].ID == id {
			if tl.Items[i].Completed {
				return fmt.Errorf("todo with ID %d is already completed", id)
			}
			
			now := time.Now()
			tl.Items[i].Completed = true
			tl.Items[i].CompletedAt = &now
			return nil
		}
	}
	return fmt.Errorf("todo with ID %d not found", id)
}

// DeleteTodo removes a todo item
func (tl *TodoList) DeleteTodo(id int) error {
	for i, item := range tl.Items {
		if item.ID == id {
			// Remove item from slice
			tl.Items = append(tl.Items[:i], tl.Items[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("todo with ID %d not found", id)
}

// ListTodos returns todos based on filter
func (tl *TodoList) ListTodos(filter string) []TodoItem {
	var filtered []TodoItem
	
	for _, item := range tl.Items {
		switch filter {
		case "completed":
			if item.Completed {
				filtered = append(filtered, item)
			}
		case "pending":
			if !item.Completed {
				filtered = append(filtered, item)
			}
		case "high":
			if item.Priority == "high" {
				filtered = append(filtered, item)
			}
		default: // "all"
			filtered = append(filtered, item)
		}
	}
	
	return filtered
}

// GetStats returns statistics about todos
func (tl *TodoList) GetStats() map[string]int {
	stats := map[string]int{
		"total":     len(tl.Items),
		"completed": 0,
		"pending":   0,
		"high":      0,
		"medium":    0,
		"low":       0,
	}
	
	for _, item := range tl.Items {
		if item.Completed {
			stats["completed"]++
		} else {
			stats["pending"]++
		}
		
		stats[item.Priority]++
	}
	
	return stats
}

// DISPLAY FUNCTIONS

// DisplayTodo prints a single todo item
func DisplayTodo(todo TodoItem) {
	status := "❌ Pending"
	if todo.Completed {
		status = "✅ Completed"
	}
	
	priority := strings.ToUpper(todo.Priority)
	priorityIcon := "🔵" // medium
	if todo.Priority == "high" {
		priorityIcon = "🔴"
	} else if todo.Priority == "low" {
		priorityIcon = "🟢"
	}
	
	fmt.Printf("ID: %d | %s %s [%s]\n", todo.ID, status, priorityIcon, priority)
	fmt.Printf("Title: %s\n", todo.Title)
	if todo.Description != "" {
		fmt.Printf("Description: %s\n", todo.Description)
	}
	fmt.Printf("Created: %s\n", todo.CreatedAt.Format("2006-01-02 15:04:05"))
	if todo.CompletedAt != nil {
		fmt.Printf("Completed: %s\n", todo.CompletedAt.Format("2006-01-02 15:04:05"))
	}
	fmt.Println(strings.Repeat("-", 50))
}

// DisplayTodoList prints a list of todos
func DisplayTodoList(todos []TodoItem, title string) {
	fmt.Printf("\n=== %s ===\n", title)
	
	if len(todos) == 0 {
		fmt.Println("No todos found.")
		return
	}
	
	for _, todo := range todos {
		DisplayTodo(todo)
	}
}

// DisplayStats prints todo statistics
func DisplayStats(stats map[string]int) {
	fmt.Println("\n=== Todo Statistics ===")
	fmt.Printf("Total todos: %d\n", stats["total"])
	fmt.Printf("Completed: %d\n", stats["completed"])
	fmt.Printf("Pending: %d\n", stats["pending"])
	fmt.Printf("High priority: %d\n", stats["high"])
	fmt.Printf("Medium priority: %d\n", stats["medium"])
	fmt.Printf("Low priority: %d\n", stats["low"])
	
	if stats["total"] > 0 {
		completionRate := float64(stats["completed"]) / float64(stats["total"]) * 100
		fmt.Printf("Completion rate: %.1f%%\n", completionRate)
	}
}

// COMMAND LINE INTERFACE

// ShowUsage displays help information
func ShowUsage() {
	fmt.Println("Todo CLI Application")
	fmt.Println("Usage: go run todo_app.go <command> [arguments]")
	fmt.Println()
	fmt.Println("Commands:")
	fmt.Println("  add <title> [description] [priority]  - Add a new todo")
	fmt.Println("  list [filter]                        - List todos (all/pending/completed/high)")
	fmt.Println("  complete <id>                        - Mark todo as completed")
	fmt.Println("  delete <id>                          - Delete a todo")
	fmt.Println("  stats                                - Show statistics")
	fmt.Println("  help                                 - Show this help")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  go run todo_app.go add \"Buy groceries\" \"Milk, bread, eggs\" high")
	fmt.Println("  go run todo_app.go list pending")
	fmt.Println("  go run todo_app.go complete 1")
	fmt.Println("  go run todo_app.go delete 2")
}

// Main function
func main() {
	// Check if any arguments provided
	if len(os.Args) < 2 {
		ShowUsage()
		return
	}
	
	// Initialize todo list
	todoList := NewTodoList("todos.json")
	
	// Load existing todos
	if err := todoList.LoadFromFile(); err != nil {
		fmt.Printf("Error loading todos: %v\n", err)
		return
	}
	
	// Parse command
	command := os.Args[1]
	
	switch command {
	case "add":
		if len(os.Args) < 3 {
			fmt.Println("Error: Title is required for add command")
			fmt.Println("Usage: go run todo_app.go add <title> [description] [priority]")
			return
		}
		
		title := os.Args[2]
		description := ""
		priority := "medium"
		
		if len(os.Args) > 3 {
			description = os.Args[3]
		}
		if len(os.Args) > 4 {
			priority = os.Args[4]
		}
		
		todo := todoList.AddTodo(title, description, priority)
		
		if err := todoList.SaveToFile(); err != nil {
			fmt.Printf("Error saving todos: %v\n", err)
			return
		}
		
		fmt.Printf("✅ Added todo: %s (ID: %d)\n", todo.Title, todo.ID)
		
	case "list":
		filter := "all"
		if len(os.Args) > 2 {
			filter = os.Args[2]
		}
		
		todos := todoList.ListTodos(filter)
		DisplayTodoList(todos, fmt.Sprintf("Todos (%s)", filter))
		
	case "complete":
		if len(os.Args) < 3 {
			fmt.Println("Error: ID is required for complete command")
			fmt.Println("Usage: go run todo_app.go complete <id>")
			return
		}
		
		id, err := strconv.Atoi(os.Args[2])
		if err != nil {
			fmt.Printf("Error: Invalid ID '%s'\n", os.Args[2])
			return
		}
		
		err = todoList.CompleteTodo(id)
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			return
		}
		
		if err := todoList.SaveToFile(); err != nil {
			fmt.Printf("Error saving todos: %v\n", err)
			return
		}
		
		fmt.Printf("✅ Completed todo with ID %d\n", id)
		
	case "delete":
		if len(os.Args) < 3 {
			fmt.Println("Error: ID is required for delete command")
			fmt.Println("Usage: go run todo_app.go delete <id>")
			return
		}
		
		id, err := strconv.Atoi(os.Args[2])
		if err != nil {
			fmt.Printf("Error: Invalid ID '%s'\n", os.Args[2])
			return
		}
		
		err = todoList.DeleteTodo(id)
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			return
		}
		
		if err := todoList.SaveToFile(); err != nil {
			fmt.Printf("Error saving todos: %v\n", err)
			return
		}
		
		fmt.Printf("🗑️ Deleted todo with ID %d\n", id)
		
	case "stats":
		stats := todoList.GetStats()
		DisplayStats(stats)
		
	case "help":
		ShowUsage()
		
	default:
		fmt.Printf("Error: Unknown command '%s'\n", command)
		ShowUsage()
	}
}
