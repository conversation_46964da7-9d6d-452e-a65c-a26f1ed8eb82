// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Struct for demonstration
type Student struct {
	Name  string
	Grade int
}

// FUNCTIONS DEMONSTRATING POINTER CONCEPTS

// Function that takes a value (makes a copy)
func updateGradeByValue(s Student, newGrade int) {
	fmt.Printf("Inside updateGradeByValue: changing %s's grade from %d to %d\n", 
		s.Name, s.Grade, newGrade)
	s.Grade = newGrade // This only changes the copy
	fmt.Printf("Inside function: %s's grade is now %d\n", s.Name, s.Grade)
}

// Function that takes a pointer (can modify original)
func updateGradeByPointer(s *Student, newGrade int) {
	fmt.Printf("Inside updateGradeByPointer: changing %s's grade from %d to %d\n", 
		s.Name, s.Grade, newGrade)
	s.Grade = newGrade // This changes the original
	fmt.Printf("Inside function: %s's grade is now %d\n", s.Name, s.Grade)
}

// Function that returns a pointer to a new struct
func createStudent(name string, grade int) *Student {
	// It's safe to return pointer to local variable in Go
	// Go's garbage collector handles memory management
	student := Student{Name: name, Grade: grade}
	return &student
}

// Function demonstrating pointer arithmetic concepts
func demonstratePointerBasics() {
	fmt.Println("=== Pointer Basics ===")
	
	// Regular variable
	x := 42
	fmt.Printf("x = %d\n", x)
	
	// Pointer to x
	var p *int    // Declare pointer to int
	p = &x        // & gets the address of x
	
	fmt.Printf("Address of x: %p\n", &x)
	fmt.Printf("Value of p (pointer): %p\n", p)
	fmt.Printf("Value pointed to by p: %d\n", *p) // * dereferences the pointer
	
	// Modify x through the pointer
	*p = 100
	fmt.Printf("After *p = 100, x = %d\n", x)
	
	fmt.Println()
}

// Function showing nil pointers
func demonstrateNilPointers() {
	fmt.Println("=== Nil Pointers ===")
	
	var p *int
	fmt.Printf("Uninitialized pointer p: %v\n", p)
	fmt.Printf("Is p nil? %t\n", p == nil)
	
	// Safe way to check and use pointer
	if p != nil {
		fmt.Printf("Value: %d\n", *p)
	} else {
		fmt.Println("Pointer is nil, cannot dereference")
	}
	
	// Initialize the pointer
	x := 25
	p = &x
	fmt.Printf("After initialization, p points to: %d\n", *p)
	
	fmt.Println()
}

// Main function
func main() {
	fmt.Println("=== POINTERS AND THEIR USAGE ===")
	fmt.Println()
	
	// BASIC POINTER CONCEPTS
	demonstratePointerBasics()
	demonstrateNilPointers()
	
	// VALUE vs POINTER PARAMETERS
	fmt.Println("=== Value vs Pointer Parameters ===")
	
	student := Student{Name: "Alice", Grade: 85}
	fmt.Printf("Original: %s has grade %d\n", student.Name, student.Grade)
	fmt.Println()
	
	// Try to update using value parameter
	fmt.Println("Calling updateGradeByValue:")
	updateGradeByValue(student, 95)
	fmt.Printf("After updateGradeByValue: %s has grade %d\n", student.Name, student.Grade)
	fmt.Println("Notice: Original unchanged because function got a copy")
	fmt.Println()
	
	// Update using pointer parameter
	fmt.Println("Calling updateGradeByPointer:")
	updateGradeByPointer(&student, 95) // &student gets the address
	fmt.Printf("After updateGradeByPointer: %s has grade %d\n", student.Name, student.Grade)
	fmt.Println("Notice: Original changed because function got the address")
	fmt.Println()
	
	// CREATING STRUCTS WITH POINTERS
	fmt.Println("=== Creating Structs with Pointers ===")
	
	// Method 1: Create struct then get pointer
	student1 := Student{Name: "Bob", Grade: 78}
	studentPtr1 := &student1
	
	// Method 2: Create pointer directly with new()
	studentPtr2 := new(Student)
	studentPtr2.Name = "Charlie"
	studentPtr2.Grade = 92
	
	// Method 3: Use function that returns pointer
	studentPtr3 := createStudent("Diana", 88)
	
	fmt.Printf("Student 1: %+v (address: %p)\n", *studentPtr1, studentPtr1)
	fmt.Printf("Student 2: %+v (address: %p)\n", *studentPtr2, studentPtr2)
	fmt.Printf("Student 3: %+v (address: %p)\n", *studentPtr3, studentPtr3)
	fmt.Println()
	
	// POINTER RECEIVERS IN METHODS
	fmt.Println("=== Pointer Receivers in Methods ===")
	
	// Create students
	students := []*Student{
		{Name: "Eve", Grade: 82},
		{Name: "Frank", Grade: 76},
		{Name: "Grace", Grade: 94},
	}
	
	fmt.Println("Before grade updates:")
	for i, s := range students {
		fmt.Printf("Student %d: %s (Grade: %d)\n", i+1, s.Name, s.Grade)
	}
	
	// Update grades using pointer function
	fmt.Println("\nUpdating grades:")
	for i, s := range students {
		newGrade := s.Grade + 5 // Give everyone 5 bonus points
		updateGradeByPointer(s, newGrade)
	}
	
	fmt.Println("\nAfter grade updates:")
	for i, s := range students {
		fmt.Printf("Student %d: %s (Grade: %d)\n", i+1, s.Name, s.Grade)
	}
	fmt.Println()
	
	// POINTER SLICES
	fmt.Println("=== Working with Pointer Slices ===")
	
	// Slice of pointers vs slice of values
	valueSlice := []Student{
		{Name: "Alice", Grade: 85},
		{Name: "Bob", Grade: 78},
	}
	
	pointerSlice := []*Student{
		{Name: "Charlie", Grade: 92},
		{Name: "Diana", Grade: 88},
	}
	
	fmt.Println("Value slice (copies when passed):")
	for _, s := range valueSlice {
		fmt.Printf("  %s: %d\n", s.Name, s.Grade)
	}
	
	fmt.Println("Pointer slice (references when passed):")
	for _, s := range pointerSlice {
		fmt.Printf("  %s: %d\n", s.Name, s.Grade)
	}
	
	// Modify through pointers
	fmt.Println("\nModifying through pointers:")
	for _, s := range pointerSlice {
		s.Grade += 10 // This modifies the original structs
	}
	
	fmt.Println("After modification:")
	for _, s := range pointerSlice {
		fmt.Printf("  %s: %d\n", s.Name, s.Grade)
	}
	fmt.Println()
	
	// MEMORY EFFICIENCY DEMONSTRATION
	fmt.Println("=== Memory Efficiency ===")
	
	// Large struct example
	type LargeStruct struct {
		Data [1000]int // Large array
		Name string
	}
	
	large := LargeStruct{Name: "Big Data"}
	
	// Function that takes value (copies entire struct)
	processLargeByValue := func(ls LargeStruct) {
		fmt.Printf("Processing %s by value (copied %d bytes)\n", 
			ls.Name, len(ls.Data)*8) // Approximate size
	}
	
	// Function that takes pointer (only copies address)
	processLargeByPointer := func(ls *LargeStruct) {
		fmt.Printf("Processing %s by pointer (copied ~8 bytes for address)\n", 
			ls.Name)
	}
	
	fmt.Println("Demonstrating memory efficiency:")
	processLargeByValue(large)    // Copies entire struct
	processLargeByPointer(&large) // Only copies pointer
	
	fmt.Println()
	
	// COMMON POINTER PATTERNS
	fmt.Println("=== Common Pointer Patterns ===")
	
	// Pattern 1: Optional values (pointer can be nil)
	var optionalStudent *Student
	
	if optionalStudent == nil {
		fmt.Println("No student assigned yet")
	}
	
	optionalStudent = &Student{Name: "Optional", Grade: 90}
	if optionalStudent != nil {
		fmt.Printf("Student assigned: %s\n", optionalStudent.Name)
	}
	
	// Pattern 2: Linked structures
	type Node struct {
		Value int
		Next  *Node // Pointer to next node
	}
	
	// Create a simple linked list
	node1 := &Node{Value: 1}
	node2 := &Node{Value: 2}
	node3 := &Node{Value: 3}
	
	node1.Next = node2
	node2.Next = node3
	
	fmt.Println("\nLinked list traversal:")
	current := node1
	for current != nil {
		fmt.Printf("Node value: %d\n", current.Value)
		current = current.Next
	}
	
	fmt.Println()
	fmt.Println("Pointers demonstration complete! 👉")
}
