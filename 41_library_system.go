// Package declaration
package main

// Import fmt for input/output
import "fmt"

// LIBRARY MANAGEMENT SYSTEM
// Demonstrates practical use of structs in a real-world scenario

// Book struct represents a book in the library
type Book struct {
	ID          int
	Title       string
	Author      string
	ISBN        string
	Genre       string
	Pages       int
	Available   bool
	BorrowedBy  string // Empty if available
}

// Member struct represents a library member
type Member struct {
	ID           int
	Name         string
	Email        string
	Phone        string
	MembershipType string // "Standard", "Premium", "Student"
	BooksCheckedOut []int  // Book IDs
	Fines        float64
}

// Library struct represents the entire library
type Library struct {
	Name    string
	Address string
	Books   []Book
	Members []Member
}

// Function to create a new book
func createBook(id int, title, author, isbn, genre string, pages int) Book {
	return Book{
		ID:        id,
		Title:     title,
		Author:    author,
		ISBN:      isbn,
		Genre:     genre,
		Pages:     pages,
		Available: true,
		BorrowedBy: "",
	}
}

// Function to create a new member
func createMember(id int, name, email, phone, membershipType string) Member {
	return Member{
		ID:             id,
		Name:           name,
		Email:          email,
		Phone:          phone,
		MembershipType: membershipType,
		BooksCheckedOut: []int{},
		Fines:          0.0,
	}
}

// Function to display book information
func displayBook(book Book) {
	status := "Available"
	if !book.Available {
		status = fmt.Sprintf("Checked out by %s", book.BorrowedBy)
	}
	
	fmt.Printf("📚 %s\n", book.Title)
	fmt.Printf("   Author: %s\n", book.Author)
	fmt.Printf("   Genre: %s | Pages: %d\n", book.Genre, book.Pages)
	fmt.Printf("   ISBN: %s\n", book.ISBN)
	fmt.Printf("   Status: %s\n", status)
}

// Function to display member information
func displayMember(member Member) {
	fmt.Printf("👤 %s (ID: %d)\n", member.Name, member.ID)
	fmt.Printf("   Email: %s\n", member.Email)
	fmt.Printf("   Phone: %s\n", member.Phone)
	fmt.Printf("   Membership: %s\n", member.MembershipType)
	fmt.Printf("   Books checked out: %d\n", len(member.BooksCheckedOut))
	if member.Fines > 0 {
		fmt.Printf("   Outstanding fines: $%.2f\n", member.Fines)
	}
}

// Function to find book by ID
func findBookByID(library Library, bookID int) (Book, bool) {
	for _, book := range library.Books {
		if book.ID == bookID {
			return book, true
		}
	}
	return Book{}, false
}

// Function to find member by ID
func findMemberByID(library Library, memberID int) (Member, bool) {
	for _, member := range library.Members {
		if member.ID == memberID {
			return member, true
		}
	}
	return Member{}, false
}

// Main function
func main() {
	fmt.Println("=== LIBRARY MANAGEMENT SYSTEM ===")
	fmt.Println()
	
	// CREATE LIBRARY
	library := Library{
		Name:    "Central Public Library",
		Address: "123 Knowledge Street, Booktown",
		Books:   []Book{},
		Members: []Member{},
	}
	
	fmt.Printf("🏛️ Welcome to %s\n", library.Name)
	fmt.Printf("📍 Located at: %s\n", library.Address)
	fmt.Println()
	
	// ADD BOOKS TO LIBRARY
	fmt.Println("=== Adding Books to Collection ===")
	
	books := []Book{
		createBook(1, "The Go Programming Language", "Alan Donovan", "978-0134190440", "Programming", 380),
		createBook(2, "Clean Code", "Robert Martin", "978-0132350884", "Programming", 464),
		createBook(3, "1984", "George Orwell", "978-0451524935", "Fiction", 328),
		createBook(4, "To Kill a Mockingbird", "Harper Lee", "978-0061120084", "Fiction", 376),
		createBook(5, "The Pragmatic Programmer", "David Thomas", "978-0201616224", "Programming", 352),
	}
	
	// Add books to library
	library.Books = append(library.Books, books...)
	
	fmt.Printf("Added %d books to the collection\n", len(books))
	fmt.Println()
	
	// ADD MEMBERS TO LIBRARY
	fmt.Println("=== Registering Members ===")
	
	members := []Member{
		createMember(101, "Alice Johnson", "<EMAIL>", "555-0101", "Premium"),
		createMember(102, "Bob Smith", "<EMAIL>", "555-0102", "Standard"),
		createMember(103, "Charlie Brown", "<EMAIL>", "555-0103", "Student"),
		createMember(104, "Diana Prince", "<EMAIL>", "555-0104", "Premium"),
	}
	
	// Add members to library
	library.Members = append(library.Members, members...)
	
	fmt.Printf("Registered %d new members\n", len(members))
	fmt.Println()
	
	// DISPLAY LIBRARY CATALOG
	fmt.Println("=== Library Catalog ===")
	for _, book := range library.Books {
		displayBook(book)
		fmt.Println()
	}
	
	// DISPLAY MEMBER LIST
	fmt.Println("=== Member Directory ===")
	for _, member := range library.Members {
		displayMember(member)
		fmt.Println()
	}
	
	// SIMULATE BOOK CHECKOUT
	fmt.Println("=== Book Checkout Simulation ===")
	
	// Alice checks out "The Go Programming Language"
	bookID := 1
	memberID := 101
	
	// Find the book and member
	book, bookFound := findBookByID(library, bookID)
	member, memberFound := findMemberByID(library, memberID)
	
	if bookFound && memberFound && book.Available {
		// Update book status
		for i := range library.Books {
			if library.Books[i].ID == bookID {
				library.Books[i].Available = false
				library.Books[i].BorrowedBy = member.Name
				break
			}
		}
		
		// Update member's checked out books
		for i := range library.Members {
			if library.Members[i].ID == memberID {
				library.Members[i].BooksCheckedOut = append(library.Members[i].BooksCheckedOut, bookID)
				break
			}
		}
		
		fmt.Printf("✅ %s checked out '%s'\n", member.Name, book.Title)
	} else {
		fmt.Println("❌ Checkout failed")
	}
	
	// Bob tries to check out the same book
	memberID = 102
	member2, _ := findMemberByID(library, memberID)
	
	book, _ = findBookByID(library, bookID) // Get updated book status
	if !book.Available {
		fmt.Printf("❌ %s cannot check out '%s' - already borrowed by %s\n", 
			member2.Name, book.Title, book.BorrowedBy)
	}
	
	fmt.Println()
	
	// LIBRARY STATISTICS
	fmt.Println("=== Library Statistics ===")
	
	totalBooks := len(library.Books)
	availableBooks := 0
	checkedOutBooks := 0
	
	for _, book := range library.Books {
		if book.Available {
			availableBooks++
		} else {
			checkedOutBooks++
		}
	}
	
	fmt.Printf("📊 Collection Statistics:\n")
	fmt.Printf("   Total Books: %d\n", totalBooks)
	fmt.Printf("   Available: %d\n", availableBooks)
	fmt.Printf("   Checked Out: %d\n", checkedOutBooks)
	
	// Member statistics
	totalMembers := len(library.Members)
	membershipTypes := make(map[string]int)
	totalFines := 0.0
	
	for _, member := range library.Members {
		membershipTypes[member.MembershipType]++
		totalFines += member.Fines
	}
	
	fmt.Printf("\n👥 Member Statistics:\n")
	fmt.Printf("   Total Members: %d\n", totalMembers)
	for memberType, count := range membershipTypes {
		fmt.Printf("   %s: %d\n", memberType, count)
	}
	fmt.Printf("   Total Outstanding Fines: $%.2f\n", totalFines)
	
	// Genre statistics
	fmt.Printf("\n📚 Genre Distribution:\n")
	genreCount := make(map[string]int)
	
	for _, book := range library.Books {
		genreCount[book.Genre]++
	}
	
	for genre, count := range genreCount {
		fmt.Printf("   %s: %d books\n", genre, count)
	}
	
	fmt.Println()
	
	// UPDATED CATALOG (showing checkout status)
	fmt.Println("=== Updated Catalog (After Checkout) ===")
	for _, book := range library.Books {
		displayBook(book)
		fmt.Println()
	}
	
	fmt.Println("Library management system demonstration complete! 📚")
}
