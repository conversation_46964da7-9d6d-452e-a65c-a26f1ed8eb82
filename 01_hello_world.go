// This line tells Go that this file belongs to the "main" package
// Every Go program starts with a package declaration
package main

// This line imports the "fmt" package which helps us print text to screen
// fmt stands for "format" and contains functions for input/output
import "fmt"

// This is the main function - it's like the starting point of our program
// When you run a Go program, it automatically looks for and runs the main function
func main() {
	// This line prints "Hello, World!" to the screen
	// fmt.Println means "format print line" - it prints text and adds a new line
	fmt.Println("Hello, World!")
	
	// This prints a welcome message
	// You can print any text by putting it inside double quotes
	fmt.Println("Welcome to Go programming!")
	
	// This shows how to print multiple things in one line
	// The comma separates different items to print
	fmt.Println("Go", "is", "awesome!")
}
