// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// TEXT ANALYZER
	// Demonstrates advanced use of range with strings and maps
	
	fmt.Println("=== TEXT ANALYZER ===")
	fmt.Println()
	
	// Sample text to analyze
	text := "Hello World! This is a sample text for analysis. Hello again!"
	
	fmt.Printf("Analyzing text: \"%s\"\n", text)
	fmt.Printf("Text length: %d characters\n", len(text))
	fmt.Println()
	
	// CHARACTER ANALYSIS
	fmt.Println("=== Character Analysis ===")
	
	// Count different types of characters
	letterCount := 0
	digitCount := 0
	spaceCount := 0
	punctuationCount := 0
	
	// Character frequency map
	charFreq := make(map[rune]int)
	
	// Analyze each character
	for _, char := range text {
		// Count character frequency
		charFreq[char]++
		
		// Categorize characters
		if (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') {
			letterCount++
		} else if char >= '0' && char <= '9' {
			digitCount++
		} else if char == ' ' {
			spaceCount++
		} else {
			punctuationCount++
		}
	}
	
	// Display character statistics
	fmt.Printf("Letters: %d\n", letterCount)
	fmt.Printf("Digits: %d\n", digitCount)
	fmt.Printf("Spaces: %d\n", spaceCount)
	fmt.Printf("Punctuation: %d\n", punctuationCount)
	fmt.Printf("Total characters: %d\n", letterCount+digitCount+spaceCount+punctuationCount)
	
	fmt.Println()
	
	// CHARACTER FREQUENCY
	fmt.Println("=== Character Frequency ===")
	fmt.Println("Most common characters:")
	
	// Find top 5 most frequent characters
	type CharCount struct {
		char  rune
		count int
	}
	
	// Convert map to slice for sorting
	var charCounts []CharCount
	for char, count := range charFreq {
		charCounts = append(charCounts, CharCount{char, count})
	}
	
	// Simple bubble sort to find most frequent
	for i := 0; i < len(charCounts)-1; i++ {
		for j := 0; j < len(charCounts)-i-1; j++ {
			if charCounts[j].count < charCounts[j+1].count {
				charCounts[j], charCounts[j+1] = charCounts[j+1], charCounts[j]
			}
		}
	}
	
	// Display top 5 characters
	maxDisplay := 5
	if len(charCounts) < maxDisplay {
		maxDisplay = len(charCounts)
	}
	
	for i := 0; i < maxDisplay; i++ {
		char := charCounts[i].char
		count := charCounts[i].count
		
		// Display character nicely
		if char == ' ' {
			fmt.Printf("'space': %d times\n", count)
		} else {
			fmt.Printf("'%c': %d times\n", char, count)
		}
	}
	
	fmt.Println()
	
	// WORD ANALYSIS
	fmt.Println("=== Word Analysis ===")
	
	// Simple word splitting (split by spaces)
	words := []string{}
	currentWord := ""
	
	// Extract words from text
	for _, char := range text {
		if char == ' ' || char == '!' || char == '.' || char == ',' || char == '?' {
			if currentWord != "" {
				words = append(words, currentWord)
				currentWord = ""
			}
		} else {
			currentWord += string(char)
		}
	}
	
	// Don't forget the last word
	if currentWord != "" {
		words = append(words, currentWord)
	}
	
	fmt.Printf("Total words: %d\n", len(words))
	
	// Word frequency
	wordFreq := make(map[string]int)
	for _, word := range words {
		// Convert to lowercase for case-insensitive counting
		lowerWord := ""
		for _, char := range word {
			if char >= 'A' && char <= 'Z' {
				lowerWord += string(char + 32) // Convert to lowercase
			} else {
				lowerWord += string(char)
			}
		}
		wordFreq[lowerWord]++
	}
	
	fmt.Println("\nWord frequency:")
	for word, count := range wordFreq {
		if count > 1 {
			fmt.Printf("'%s': appears %d times\n", word, count)
		} else {
			fmt.Printf("'%s': appears %d time\n", word, count)
		}
	}
	
	fmt.Println()
	
	// WORD LENGTH ANALYSIS
	fmt.Println("=== Word Length Analysis ===")
	
	lengthFreq := make(map[int]int)
	totalLength := 0
	longestWord := ""
	shortestWord := words[0]
	
	for _, word := range words {
		length := len(word)
		lengthFreq[length]++
		totalLength += length
		
		if length > len(longestWord) {
			longestWord = word
		}
		
		if length < len(shortestWord) {
			shortestWord = word
		}
	}
	
	averageLength := float64(totalLength) / float64(len(words))
	
	fmt.Printf("Average word length: %.1f characters\n", averageLength)
	fmt.Printf("Longest word: '%s' (%d characters)\n", longestWord, len(longestWord))
	fmt.Printf("Shortest word: '%s' (%d characters)\n", shortestWord, len(shortestWord))
	
	fmt.Println("\nWord length distribution:")
	for length := 1; length <= 10; length++ {
		if count, exists := lengthFreq[length]; exists {
			fmt.Printf("%d characters: %d words\n", length, count)
		}
	}
	
	fmt.Println()
	
	// TEXT STATISTICS SUMMARY
	fmt.Println("=== Summary ===")
	fmt.Printf("📝 Text: \"%s\"\n", text)
	fmt.Printf("📊 Statistics:\n")
	fmt.Printf("   • Total characters: %d\n", len(text))
	fmt.Printf("   • Total words: %d\n", len(words))
	fmt.Printf("   • Unique words: %d\n", len(wordFreq))
	fmt.Printf("   • Average word length: %.1f characters\n", averageLength)
	fmt.Printf("   • Letters: %d (%.1f%%)\n", letterCount, 
		float64(letterCount)/float64(len(text))*100)
	fmt.Printf("   • Spaces: %d (%.1f%%)\n", spaceCount, 
		float64(spaceCount)/float64(len(text))*100)
	
	fmt.Println()
	fmt.Println("Text analysis complete! 📈")
}
