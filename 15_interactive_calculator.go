// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// Interactive Calculator Program
	// This program asks user for input and performs calculations
	
	// Variables to store user input
	var num1, num2 float64
	var operation string
	
	// Display welcome message
	fmt.Println("=== INTERACTIVE CALCULATOR ===")
	fmt.Println("This calculator can add, subtract, multiply, and divide")
	fmt.Println()
	
	// Get first number from user
	fmt.Print("Enter the first number: ")
	// fmt.Scan reads the input and stores it in the variable
	fmt.Scan(&num1)
	
	// Get operation from user
	fmt.Print("Enter operation (+, -, *, /): ")
	fmt.Scan(&operation)
	
	// Get second number from user
	fmt.Print("Enter the second number: ")
	fmt.Scan(&num2)
	
	// Display what the user entered
	fmt.Println()
	fmt.Printf("You entered: %.2f %s %.2f\n", num1, operation, num2)
	
	// Perform the calculation based on the operation
	// We'll use simple if statements (you'll learn more about these later)
	var result float64
	var isValidOperation bool = true
	
	if operation == "+" {
		result = num1 + num2
	} else if operation == "-" {
		result = num1 - num2
	} else if operation == "*" {
		result = num1 * num2
	} else if operation == "/" {
		if num2 != 0 {
			result = num1 / num2
		} else {
			fmt.Println("Error: Cannot divide by zero!")
			isValidOperation = false
		}
	} else {
		fmt.Println("Error: Invalid operation!")
		isValidOperation = false
	}
	
	// Display the result if operation was valid
	if isValidOperation {
		fmt.Printf("Result: %.2f %s %.2f = %.2f\n", num1, operation, num2, result)
	}
	
	fmt.Println()
	fmt.Println("Thank you for using the calculator!")
}
