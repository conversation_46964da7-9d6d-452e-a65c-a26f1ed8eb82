// Package declaration
package main

// Import fmt for input/output
import "fmt"

// NAMED RETURN VALUES
// Instead of just specifying types, we give names to return values

// Basic named return example
func calculateCircle(radius float64) (area float64, circumference float64) {
	// Named return values are automatically initialized to zero values
	// area = 0.0, circumference = 0.0
	
	const pi = 3.14159
	
	// Assign values to named return variables
	area = pi * radius * radius
	circumference = 2 * pi * radius
	
	// Naked return - returns the named values
	return
}

// Named returns with early exit
func divideWithValidation(a, b float64) (result float64, valid bool, message string) {
	// Check for division by zero
	if b == 0 {
		// We can set specific values and return early
		result = 0
		valid = false
		message = "Cannot divide by zero"
		return // Naked return with the values we set
	}
	
	// Normal case
	result = a / b
	valid = true
	message = "Division successful"
	return
}

// Named returns with conditional logic
func analyzeScore(score int) (grade string, passed bool, message string) {
	// Set default values
	passed = false
	message = "Score analysis complete"
	
	// Determine grade and pass status
	switch {
	case score >= 90:
		grade = "A"
		passed = true
	case score >= 80:
		grade = "B"
		passed = true
	case score >= 70:
		grade = "C"
		passed = true
	case score >= 60:
		grade = "D"
		passed = true
	default:
		grade = "F"
		passed = false
	}
	
	return // Returns grade, passed, and message
}

// Named returns with complex calculations
func statisticsAnalysis(numbers []int) (sum int, average float64, min int, max int, count int) {
	count = len(numbers)
	
	// Handle empty slice
	if count == 0 {
		return // All values remain at zero values
	}
	
	// Initialize min and max with first element
	min = numbers[0]
	max = numbers[0]
	
	// Calculate sum, min, and max
	for _, num := range numbers {
		sum += num
		
		if num < min {
			min = num
		}
		
		if num > max {
			max = num
		}
	}
	
	// Calculate average
	average = float64(sum) / float64(count)
	
	return
}

// Named returns with error-like pattern
func parseUserInput(input string) (username string, age int, valid bool, errorMsg string) {
	// Default to invalid
	valid = false
	errorMsg = "Unknown error"
	
	// Simple parsing simulation
	if len(input) < 5 {
		errorMsg = "Input too short"
		return
	}
	
	// Simulate parsing "username:age" format
	colonIndex := -1
	for i, char := range input {
		if char == ':' {
			colonIndex = i
			break
		}
	}
	
	if colonIndex == -1 {
		errorMsg = "Invalid format, expected 'username:age'"
		return
	}
	
	username = input[:colonIndex]
	ageStr := input[colonIndex+1:]
	
	// Simple age parsing (convert string to int)
	age = 0
	for _, char := range ageStr {
		if char >= '0' && char <= '9' {
			age = age*10 + int(char-'0')
		} else {
			errorMsg = "Age must be a number"
			return
		}
	}
	
	if age < 0 || age > 150 {
		errorMsg = "Age must be between 0 and 150"
		return
	}
	
	// If we get here, everything is valid
	valid = true
	errorMsg = "Parsing successful"
	return
}

// Main function
func main() {
	fmt.Println("=== NAMED RETURN VALUES ===")
	fmt.Println()
	
	// BASIC NAMED RETURNS
	fmt.Println("=== Circle Calculations ===")
	
	radii := []float64{1.0, 2.5, 5.0, 10.0}
	
	for _, radius := range radii {
		area, circumference := calculateCircle(radius)
		fmt.Printf("Radius: %.1f → Area: %.2f, Circumference: %.2f\n", 
			radius, area, circumference)
	}
	
	fmt.Println()
	
	// NAMED RETURNS WITH VALIDATION
	fmt.Println("=== Division with Validation ===")
	
	testCases := [][2]float64{
		{10.0, 2.0},
		{15.0, 3.0},
		{8.0, 0.0}, // Division by zero
		{20.0, 4.0},
	}
	
	for _, testCase := range testCases {
		a, b := testCase[0], testCase[1]
		result, valid, message := divideWithValidation(a, b)
		
		fmt.Printf("%.1f ÷ %.1f: ", a, b)
		if valid {
			fmt.Printf("%.2f ✅ (%s)\n", result, message)
		} else {
			fmt.Printf("❌ %s\n", message)
		}
	}
	
	fmt.Println()
	
	// GRADE ANALYSIS
	fmt.Println("=== Grade Analysis ===")
	
	scores := []int{95, 87, 73, 65, 45, 92, 58}
	
	for _, score := range scores {
		grade, passed, message := analyzeScore(score)
		
		status := "❌ Failed"
		if passed {
			status = "✅ Passed"
		}
		
		fmt.Printf("Score %d: Grade %s, %s (%s)\n", score, grade, status, message)
	}
	
	fmt.Println()
	
	// STATISTICS ANALYSIS
	fmt.Println("=== Statistics Analysis ===")
	
	datasets := [][]int{
		{1, 2, 3, 4, 5},
		{10, 25, 15, 30, 20},
		{100, 50, 75, 25, 90, 60},
		{}, // Empty dataset
	}
	
	for i, data := range datasets {
		sum, average, min, max, count := statisticsAnalysis(data)
		
		fmt.Printf("Dataset %d: %v\n", i+1, data)
		if count > 0 {
			fmt.Printf("  Count: %d, Sum: %d, Average: %.2f\n", count, sum, average)
			fmt.Printf("  Min: %d, Max: %d, Range: %d\n", min, max, max-min)
		} else {
			fmt.Printf("  Empty dataset - no statistics available\n")
		}
		fmt.Println()
	}
	
	// USER INPUT PARSING
	fmt.Println("=== User Input Parsing ===")
	
	inputs := []string{
		"alice:25",
		"bob:30",
		"charlie", // Missing age
		"diana:abc", // Invalid age
		"eve:200", // Age too high
		"frank:28",
		"x:5", // Too short overall but valid format
	}
	
	for _, input := range inputs {
		username, age, valid, errorMsg := parseUserInput(input)
		
		fmt.Printf("Input: \"%s\"\n", input)
		if valid {
			fmt.Printf("  ✅ Username: %s, Age: %d (%s)\n", username, age, errorMsg)
		} else {
			fmt.Printf("  ❌ Error: %s\n", errorMsg)
		}
		fmt.Println()
	}
	
	// DEMONSTRATING ZERO VALUES
	fmt.Println("=== Zero Values in Named Returns ===")
	
	// Call function that returns early with zero values
	emptyData := []int{}
	sum, average, min, max, count := statisticsAnalysis(emptyData)
	
	fmt.Printf("Empty dataset results:\n")
	fmt.Printf("  Sum: %d (zero value for int)\n", sum)
	fmt.Printf("  Average: %.2f (zero value for float64)\n", average)
	fmt.Printf("  Min: %d (zero value for int)\n", min)
	fmt.Printf("  Max: %d (zero value for int)\n", max)
	fmt.Printf("  Count: %d\n", count)
	
	fmt.Println()
	fmt.Println("Named return values demonstration complete! 🏷️")
}
