// Package declaration
package main

// Import fmt for input/output
import "fmt"

// STRUCT DEFINITIONS
// Define structs outside of functions (usually at package level)

// Basic struct definition
type Person struct {
	Name string  // Field name and type
	Age  int     // Each field on its own line
	City string
}

// Struct with different field types
type Product struct {
	ID          int
	Name        string
	Price       float64
	InStock     bool
	Categories  []string // Slice field
}

// Struct with embedded struct (composition)
type Address struct {
	Street  string
	City    string
	State   string
	ZipCode string
}

type Employee struct {
	ID       int
	Name     string
	Position string
	Salary   float64
	Address  Address // Embedded struct
}

// Struct with anonymous fields (less common)
type Point struct {
	X, Y float64 // Multiple fields of same type on one line
}

// Main function
func main() {
	fmt.Println("=== STRUCTS AND FIELDS ===")
	fmt.Println()
	
	// CREATING STRUCT INSTANCES
	
	// Method 1: Zero value initialization
	var person1 Person
	fmt.Printf("Zero value person: %+v\n", person1)
	// %+v prints field names and values
	
	// Method 2: Struct literal with field names
	person2 := Person{
		Name: "Alice",
		Age:  28,
		City: "New York",
	}
	fmt.Printf("Person2: %+v\n", person2)
	
	// Method 3: Struct literal without field names (positional)
	person3 := Person{"Bob", 32, "Los Angeles"}
	fmt.Printf("Person3: %+v\n", person3)
	
	// Method 4: Using new() function (returns pointer)
	person4 := new(Person)
	person4.Name = "Charlie"
	person4.Age = 25
	person4.City = "Chicago"
	fmt.Printf("Person4: %+v\n", *person4)
	
	fmt.Println()
	
	// ACCESSING AND MODIFYING FIELDS
	fmt.Println("=== Accessing and Modifying Fields ===")
	
	// Access fields using dot notation
	fmt.Printf("Person2's name: %s\n", person2.Name)
	fmt.Printf("Person2's age: %d\n", person2.Age)
	
	// Modify fields
	person2.Age = 29 // Happy birthday!
	person2.City = "Boston" // Moved to a new city
	
	fmt.Printf("Updated Person2: %+v\n", person2)
	
	fmt.Println()
	
	// COMPLEX STRUCT EXAMPLE
	fmt.Println("=== Product Catalog ===")
	
	// Create products
	laptop := Product{
		ID:         1,
		Name:       "Gaming Laptop",
		Price:      1299.99,
		InStock:    true,
		Categories: []string{"Electronics", "Computers", "Gaming"},
	}
	
	book := Product{
		ID:         2,
		Name:       "Go Programming Guide",
		Price:      39.99,
		InStock:    false,
		Categories: []string{"Books", "Programming", "Technology"},
	}
	
	// Display products
	fmt.Printf("Product 1: %+v\n", laptop)
	fmt.Printf("Product 2: %+v\n", book)
	
	// Access slice fields
	fmt.Printf("Laptop categories: %v\n", laptop.Categories)
	fmt.Printf("Number of categories: %d\n", len(laptop.Categories))
	
	// Modify slice fields
	laptop.Categories = append(laptop.Categories, "High Performance")
	fmt.Printf("Updated laptop categories: %v\n", laptop.Categories)
	
	fmt.Println()
	
	// EMBEDDED STRUCTS
	fmt.Println("=== Employee with Address ===")
	
	employee := Employee{
		ID:       101,
		Name:     "Diana Smith",
		Position: "Software Engineer",
		Salary:   85000.00,
		Address: Address{
			Street:  "123 Main St",
			City:    "Seattle",
			State:   "WA",
			ZipCode: "98101",
		},
	}
	
	fmt.Printf("Employee: %+v\n", employee)
	
	// Access embedded struct fields
	fmt.Printf("Employee name: %s\n", employee.Name)
	fmt.Printf("Employee address: %+v\n", employee.Address)
	fmt.Printf("Employee city: %s\n", employee.Address.City)
	
	// Modify embedded struct fields
	employee.Address.City = "Portland"
	employee.Address.State = "OR"
	employee.Salary = 90000.00 // Promotion!
	
	fmt.Printf("Updated employee: %+v\n", employee)
	
	fmt.Println()
	
	// STRUCT COMPARISON
	fmt.Println("=== Struct Comparison ===")
	
	point1 := Point{X: 3.0, Y: 4.0}
	point2 := Point{X: 3.0, Y: 4.0}
	point3 := Point{X: 1.0, Y: 2.0}
	
	fmt.Printf("Point1: %+v\n", point1)
	fmt.Printf("Point2: %+v\n", point2)
	fmt.Printf("Point3: %+v\n", point3)
	
	// Compare structs (all fields must be comparable)
	fmt.Printf("Point1 == Point2: %t\n", point1 == point2)
	fmt.Printf("Point1 == Point3: %t\n", point1 == point3)
	
	fmt.Println()
	
	// STRUCT SLICES
	fmt.Println("=== Working with Struct Slices ===")
	
	// Create a slice of structs
	people := []Person{
		{"Alice", 28, "New York"},
		{"Bob", 32, "Los Angeles"},
		{"Charlie", 25, "Chicago"},
		{"Diana", 30, "Seattle"},
	}
	
	fmt.Println("People in our database:")
	for i, person := range people {
		fmt.Printf("%d. %s (age %d) from %s\n", 
			i+1, person.Name, person.Age, person.City)
	}
	
	// Add new person
	newPerson := Person{
		Name: "Eve",
		Age:  27,
		City: "Miami",
	}
	people = append(people, newPerson)
	
	fmt.Printf("\nAfter adding Eve: %d people total\n", len(people))
	
	// Find people by criteria
	fmt.Println("\nPeople over 30:")
	for _, person := range people {
		if person.Age > 30 {
			fmt.Printf("- %s (age %d)\n", person.Name, person.Age)
		}
	}
	
	fmt.Println()
	
	// ANONYMOUS STRUCTS
	fmt.Println("=== Anonymous Structs ===")
	
	// Define and use struct without naming the type
	config := struct {
		Host     string
		Port     int
		Database string
		SSL      bool
	}{
		Host:     "localhost",
		Port:     5432,
		Database: "myapp",
		SSL:      true,
	}
	
	fmt.Printf("Database config: %+v\n", config)
	fmt.Printf("Connection string: %s:%d/%s (SSL: %t)\n", 
		config.Host, config.Port, config.Database, config.SSL)
	
	fmt.Println()
	fmt.Println("Structs and fields demonstration complete! 📋")
}
