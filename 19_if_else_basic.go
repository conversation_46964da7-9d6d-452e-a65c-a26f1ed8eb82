// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// IF STATEMENTS - Execute code only if condition is true
	
	// Simple if statement
	age := 18
	
	// if keyword followed by condition in parentheses (optional in Go)
	// Code inside {} runs only if condition is true
	if age >= 18 {
		fmt.Println("You are an adult!")
	}
	
	// IF-ELSE - Execute one block or another
	score := 85
	
	// If condition is true, run first block
	// If condition is false, run else block
	if score >= 60 {
		fmt.Println("You passed the exam!")
	} else {
		fmt.Println("You failed the exam. Try again!")
	}
	
	// IF-ELSE IF-ELSE - Multiple conditions
	temperature := 75
	
	// Check multiple conditions in order
	// First true condition wins, others are skipped
	if temperature > 80 {
		fmt.Println("It's hot outside!")
	} else if temperature > 60 {
		fmt.Println("It's nice weather!")
	} else if temperature > 40 {
		fmt.Println("It's cool outside!")
	} else {
		fmt.Println("It's cold outside!")
	}
	
	// NESTED IF STATEMENTS - If inside another if
	hasLicense := true
	hasInsurance := true
	
	// First check if they have a license
	if hasLicense {
		fmt.Println("You have a license.")
		
		// Inside the first if, check for insurance
		if hasInsurance {
			fmt.Println("You can drive legally!")
		} else {
			fmt.Println("You need insurance to drive.")
		}
	} else {
		fmt.Println("You need a license to drive.")
	}
	
	// COMPLEX CONDITIONS with logical operators
	income := 50000
	creditScore := 720
	hasJob := true
	
	// Multiple conditions combined with && (AND) and || (OR)
	if income > 40000 && creditScore > 700 && hasJob {
		fmt.Println("Loan approved!")
	} else if income > 30000 && creditScore > 650 {
		fmt.Println("Loan approved with higher interest rate.")
	} else {
		fmt.Println("Loan denied. Please improve your credit score.")
	}
}
