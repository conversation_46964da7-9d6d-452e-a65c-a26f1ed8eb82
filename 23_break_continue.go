// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// BREAK and CONTINUE - Control loop execution
	
	// BREAK - Exit the loop completely
	fmt.Println("=== Break Example ===")
	fmt.Println("Looking for the number 7...")
	
	for i := 1; i <= 10; i++ {
		fmt.Printf("Checking number %d\n", i)
		
		// If we find 7, stop the loop
		if i == 7 {
			fmt.Println("Found 7! Stopping the search.")
			break // Exit the loop immediately
		}
	}
	fmt.Println("Loop ended.")
	
	fmt.Println()
	
	// CONTINUE - Skip to next iteration
	fmt.Println("=== Continue Example ===")
	fmt.Println("Printing odd numbers from 1 to 10:")
	
	for i := 1; i <= 10; i++ {
		// If number is even, skip to next iteration
		if i%2 == 0 {
			continue // Skip the rest of this iteration
		}
		
		// This only runs for odd numbers
		fmt.Printf("Odd number: %d\n", i)
	}
	
	fmt.Println()
	
	// PRACTICAL EXAMPLE - Password attempts
	fmt.Println("=== Password System ===")
	correctPassword := "secret123"
	maxAttempts := 3
	
	for attempt := 1; attempt <= maxAttempts; attempt++ {
		// Simulate user input (in real program, you'd use fmt.Scan)
		var userPassword string
		
		// Simulate different passwords for each attempt
		switch attempt {
		case 1:
			userPassword = "wrong1"
		case 2:
			userPassword = "wrong2"
		case 3:
			userPassword = "secret123"
		}
		
		fmt.Printf("Attempt %d: Trying password '%s'\n", attempt, userPassword)
		
		// Check if password is correct
		if userPassword == correctPassword {
			fmt.Println("✅ Login successful!")
			break // Exit loop on successful login
		} else {
			fmt.Printf("❌ Wrong password. %d attempts remaining.\n", maxAttempts-attempt)
			
			// If this was the last attempt
			if attempt == maxAttempts {
				fmt.Println("🔒 Account locked due to too many failed attempts.")
			}
		}
	}
	
	fmt.Println()
	
	// NESTED LOOPS with break and continue
	fmt.Println("=== Finding Prime Numbers ===")
	fmt.Println("Prime numbers from 2 to 20:")
	
	// Check each number from 2 to 20
	for num := 2; num <= 20; num++ {
		isPrime := true
		
		// Check if num is divisible by any number from 2 to num-1
		for divisor := 2; divisor < num; divisor++ {
			if num%divisor == 0 {
				// Found a divisor, so it's not prime
				isPrime = false
				break // No need to check more divisors
			}
		}
		
		// If it's prime, print it
		if isPrime {
			fmt.Printf("%d ", num)
		}
	}
	fmt.Println()
	
	fmt.Println()
	
	// ADVANCED EXAMPLE - Menu system
	fmt.Println("=== Interactive Menu System ===")
	
	for {
		// Display menu
		fmt.Println("\n--- MAIN MENU ---")
		fmt.Println("1. View Profile")
		fmt.Println("2. Settings")
		fmt.Println("3. Help")
		fmt.Println("4. Exit")
		
		// Simulate user choice (in real program, use fmt.Scan)
		choice := 4 // Simulate choosing exit
		
		fmt.Printf("You selected: %d\n", choice)
		
		switch choice {
		case 1:
			fmt.Println("📋 Displaying user profile...")
			continue // Go back to menu
		case 2:
			fmt.Println("⚙️ Opening settings...")
			continue // Go back to menu
		case 3:
			fmt.Println("❓ Showing help information...")
			continue // Go back to menu
		case 4:
			fmt.Println("👋 Goodbye!")
			break // Exit the menu loop
		default:
			fmt.Println("❌ Invalid choice. Please try again.")
			continue // Go back to menu
		}
		
		// This break exits the infinite loop
		break
	}
}
