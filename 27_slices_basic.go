// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// SLICES - Dynamic arrays that can grow and shrink
	
	// CREATING SLICES
	
	// Method 1: Using make() function
	// make([]type, length, capacity)
	numbers := make([]int, 5) // Creates slice with 5 zeros
	fmt.Println("Empty slice:", numbers)
	fmt.Printf("Length: %d, Capacity: %d\n", len(numbers), cap(numbers))
	
	// Method 2: Slice literal (like array but no size)
	fruits := []string{"apple", "banana", "orange"}
	fmt.Println("Fruit slice:", fruits)
	fmt.Printf("Length: %d, Capacity: %d\n", len(fruits), cap(fruits))
	
	// Method 3: From an array
	arr := [5]int{1, 2, 3, 4, 5}
	slice := arr[1:4] // Elements from index 1 to 3 (4 is excluded)
	fmt.Println("Slice from array:", slice)
	
	fmt.Println()
	
	// ACCESSING and MODIFYING elements (same as arrays)
	fruits[0] = "grape" // Change first element
	fmt.Println("Modified fruits:", fruits)
	
	// APPENDING elements (this is where slices shine!)
	// append() adds elements to the end of a slice
	fruits = append(fruits, "mango")
	fmt.Println("After append:", fruits)
	fmt.Printf("New length: %d, capacity: %d\n", len(fruits), cap(fruits))
	
	// Append multiple elements
	fruits = append(fruits, "kiwi", "strawberry")
	fmt.Println("After multiple append:", fruits)
	
	fmt.Println()
	
	// SLICE OPERATIONS
	
	// Creating an empty slice
	var scores []int
	fmt.Println("Empty slice:", scores)
	fmt.Printf("Is nil: %t\n", scores == nil)
	
	// Adding elements to empty slice
	scores = append(scores, 85, 92, 78)
	fmt.Println("Scores after append:", scores)
	
	// SLICING (creating sub-slices)
	// slice[start:end] - from start index to end-1
	fmt.Println("\n=== Slicing Operations ===")
	numbers = []int{10, 20, 30, 40, 50, 60}
	fmt.Println("Original:", numbers)
	
	fmt.Println("numbers[1:4]:", numbers[1:4])   // Elements 1, 2, 3
	fmt.Println("numbers[:3]:", numbers[:3])     // From start to index 2
	fmt.Println("numbers[2:]:", numbers[2:])     // From index 2 to end
	fmt.Println("numbers[:]:", numbers[:])       // Entire slice (copy)
	
	fmt.Println()
	
	// COPYING SLICES
	original := []int{1, 2, 3}
	
	// Method 1: Using copy() function
	copied := make([]int, len(original))
	copy(copied, original)
	fmt.Println("Original:", original)
	fmt.Println("Copied:", copied)
	
	// Modify original to show they're independent
	original[0] = 999
	fmt.Println("After modifying original:")
	fmt.Println("Original:", original)
	fmt.Println("Copied:", copied) // Unchanged
	
	fmt.Println()
	
	// REMOVING ELEMENTS (Go doesn't have built-in remove)
	fmt.Println("=== Removing Elements ===")
	items := []string{"a", "b", "c", "d", "e"}
	fmt.Println("Before removal:", items)
	
	// Remove element at index 2 ("c")
	indexToRemove := 2
	items = append(items[:indexToRemove], items[indexToRemove+1:]...)
	fmt.Println("After removing index 2:", items)
	
	fmt.Println()
	
	// ITERATING through slices
	fmt.Println("=== Iterating Through Slices ===")
	colors := []string{"red", "green", "blue", "yellow"}
	
	// Method 1: Traditional for loop
	fmt.Println("Using traditional for loop:")
	for i := 0; i < len(colors); i++ {
		fmt.Printf("Index %d: %s\n", i, colors[i])
	}
	
	// Method 2: Range (we'll learn more about this next)
	fmt.Println("\nUsing range:")
	for index, color := range colors {
		fmt.Printf("Index %d: %s\n", index, color)
	}
}
