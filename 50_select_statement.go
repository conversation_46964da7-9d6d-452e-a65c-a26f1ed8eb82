// Package declaration
package main

// Import packages
import (
	"fmt"
	"math/rand"
	"time"
)

// Function that sends data after a delay
func sendAfterDelay(ch chan string, message string, delay time.Duration) {
	time.Sleep(delay)
	ch <- message
}

// Function that simulates different services with varying response times
func callService(serviceName string, ch chan string, responseTime time.Duration) {
	fmt.Printf("📞 Calling %s service...\n", serviceName)
	time.Sleep(responseTime)
	ch <- fmt.Sprintf("Response from %s", serviceName)
}

// Function that generates random numbers
func numberGenerator(ch chan int, count int) {
	for i := 0; i < count; i++ {
		time.Sleep(time.Duration(rand.Intn(200)) * time.Millisecond)
		ch <- rand.Intn(100)
	}
	close(ch)
}

// Function that generates random strings
func stringGenerator(ch chan string, count int) {
	words := []string{"apple", "banana", "cherry", "date", "elderberry"}
	for i := 0; i < count; i++ {
		time.Sleep(time.Duration(rand.Intn(300)) * time.Millisecond)
		ch <- words[rand.Intn(len(words))]
	}
	close(ch)
}

// Main function
func main() {
	fmt.Println("=== SELECT STATEMENT IN GO ===")
	fmt.Println()
	
	// Seed random number generator
	rand.Seed(time.Now().UnixNano())
	
	// BASIC SELECT USAGE
	fmt.Println("=== Basic Select Usage ===")
	
	ch1 := make(chan string)
	ch2 := make(chan string)
	
	// Start goroutines with different delays
	go sendAfterDelay(ch1, "Message from Channel 1", 200*time.Millisecond)
	go sendAfterDelay(ch2, "Message from Channel 2", 100*time.Millisecond)
	
	// Select waits for whichever channel is ready first
	select {
	case msg1 := <-ch1:
		fmt.Printf("Received from ch1: %s\n", msg1)
	case msg2 := <-ch2:
		fmt.Printf("Received from ch2: %s\n", msg2)
	}
	
	// Wait for the other goroutine to complete
	time.Sleep(300 * time.Millisecond)
	
	fmt.Println()
	
	// SELECT WITH TIMEOUT
	fmt.Println("=== Select with Timeout ===")
	
	slowCh := make(chan string)
	
	// Start a slow operation
	go func() {
		time.Sleep(500 * time.Millisecond)
		slowCh <- "Slow operation completed"
	}()
	
	// Use select with timeout
	select {
	case result := <-slowCh:
		fmt.Printf("✅ Received: %s\n", result)
	case <-time.After(300 * time.Millisecond):
		fmt.Println("⏰ Timeout: Operation took too long")
	}
	
	// Wait for the slow operation to actually finish
	time.Sleep(300 * time.Millisecond)
	
	fmt.Println()
	
	// SELECT WITH DEFAULT CASE (NON-BLOCKING)
	fmt.Println("=== Select with Default Case ===")
	
	nonBlockingCh := make(chan string, 1)
	
	// Try to receive without blocking
	select {
	case msg := <-nonBlockingCh:
		fmt.Printf("Received: %s\n", msg)
	default:
		fmt.Println("No data available, continuing without blocking")
	}
	
	// Send data and try again
	nonBlockingCh <- "Now there's data!"
	
	select {
	case msg := <-nonBlockingCh:
		fmt.Printf("Received: %s\n", msg)
	default:
		fmt.Println("No data available")
	}
	
	fmt.Println()
	
	// MULTIPLE SERVICES WITH SELECT
	fmt.Println("=== Multiple Services with Select ===")
	
	// Simulate calling multiple services
	service1Ch := make(chan string)
	service2Ch := make(chan string)
	service3Ch := make(chan string)
	
	// Start service calls with different response times
	go callService("Database", service1Ch, 150*time.Millisecond)
	go callService("Cache", service2Ch, 50*time.Millisecond)
	go callService("API", service3Ch, 200*time.Millisecond)
	
	// Wait for the first response
	select {
	case resp := <-service1Ch:
		fmt.Printf("✅ First response: %s\n", resp)
	case resp := <-service2Ch:
		fmt.Printf("✅ First response: %s\n", resp)
	case resp := <-service3Ch:
		fmt.Printf("✅ First response: %s\n", resp)
	case <-time.After(300 * time.Millisecond):
		fmt.Println("⏰ All services timed out")
	}
	
	// Wait for other services to complete
	time.Sleep(300 * time.Millisecond)
	
	fmt.Println()
	
	// SELECT IN A LOOP (MULTIPLEXING)
	fmt.Println("=== Select in Loop (Multiplexing) ===")
	
	numberCh := make(chan int)
	stringCh := make(chan string)
	
	// Start generators
	go numberGenerator(numberCh, 3)
	go stringGenerator(stringCh, 3)
	
	// Multiplex the channels
	activeChannels := 2
	
	for activeChannels > 0 {
		select {
		case num, ok := <-numberCh:
			if ok {
				fmt.Printf("🔢 Received number: %d\n", num)
			} else {
				fmt.Println("🔢 Number channel closed")
				numberCh = nil // Disable this case
				activeChannels--
			}
		case str, ok := <-stringCh:
			if ok {
				fmt.Printf("📝 Received string: %s\n", str)
			} else {
				fmt.Println("📝 String channel closed")
				stringCh = nil // Disable this case
				activeChannels--
			}
		case <-time.After(1 * time.Second):
			fmt.Println("⏰ Timeout waiting for data")
			activeChannels = 0 // Exit loop
		}
	}
	
	fmt.Println("Multiplexing completed")
	
	fmt.Println()
	
	// SELECT FOR SENDING (NON-BLOCKING SEND)
	fmt.Println("=== Select for Sending ===")
	
	sendCh := make(chan string, 1) // Buffered channel with capacity 1
	
	// First send should succeed
	select {
	case sendCh <- "First message":
		fmt.Println("✅ First message sent successfully")
	default:
		fmt.Println("❌ Could not send first message")
	}
	
	// Second send should block (buffer is full)
	select {
	case sendCh <- "Second message":
		fmt.Println("✅ Second message sent successfully")
	default:
		fmt.Println("❌ Could not send second message (buffer full)")
	}
	
	// Receive the first message to make space
	msg := <-sendCh
	fmt.Printf("Received: %s\n", msg)
	
	// Now the second send should succeed
	select {
	case sendCh <- "Second message":
		fmt.Println("✅ Second message sent successfully")
	default:
		fmt.Println("❌ Could not send second message")
	}
	
	fmt.Println()
	
	// WORKER COORDINATION WITH SELECT
	fmt.Println("=== Worker Coordination with Select ===")
	
	workCh := make(chan string, 3)
	doneCh := make(chan bool)
	quitCh := make(chan bool)
	
	// Worker goroutine
	go func() {
		fmt.Println("👷 Worker started")
		
		for {
			select {
			case work := <-workCh:
				fmt.Printf("👷 Processing: %s\n", work)
				time.Sleep(100 * time.Millisecond)
				fmt.Printf("👷 Completed: %s\n", work)
				
			case <-quitCh:
				fmt.Println("👷 Worker received quit signal")
				doneCh <- true
				return
				
			case <-time.After(200 * time.Millisecond):
				fmt.Println("👷 Worker idle, waiting for work...")
			}
		}
	}()
	
	// Send some work
	workCh <- "Task 1"
	workCh <- "Task 2"
	
	// Wait a bit
	time.Sleep(300 * time.Millisecond)
	
	// Send quit signal
	quitCh <- true
	
	// Wait for worker to finish
	<-doneCh
	fmt.Println("Worker coordination completed")
	
	fmt.Println()
	
	// PRACTICAL EXAMPLE: LOAD BALANCER
	fmt.Println("=== Load Balancer Example ===")
	
	// Simulate multiple servers
	server1 := make(chan string)
	server2 := make(chan string)
	server3 := make(chan string)
	
	// Start servers with different response times
	go func() {
		for {
			time.Sleep(100 * time.Millisecond)
			select {
			case server1 <- "Response from Server 1":
			default:
				// Server busy, skip
			}
		}
	}()
	
	go func() {
		for {
			time.Sleep(150 * time.Millisecond)
			select {
			case server2 <- "Response from Server 2":
			default:
				// Server busy, skip
			}
		}
	}()
	
	go func() {
		for {
			time.Sleep(80 * time.Millisecond)
			select {
			case server3 <- "Response from Server 3":
			default:
				// Server busy, skip
			}
		}
	}()
	
	// Load balancer: accept first available response
	fmt.Println("Load balancer accepting first available responses:")
	
	for i := 0; i < 5; i++ {
		select {
		case resp := <-server1:
			fmt.Printf("Request %d: %s\n", i+1, resp)
		case resp := <-server2:
			fmt.Printf("Request %d: %s\n", i+1, resp)
		case resp := <-server3:
			fmt.Printf("Request %d: %s\n", i+1, resp)
		case <-time.After(300 * time.Millisecond):
			fmt.Printf("Request %d: Timeout - all servers busy\n", i+1)
		}
	}
	
	fmt.Println()
	fmt.Println("Select statement demonstration complete! 🎯")
	fmt.Println("Key takeaway: Select makes concurrent programs responsive and flexible!")
}
