// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// MAPS - Key-value pairs (like dictionaries)
	
	// CREATING MAPS
	
	// Method 1: Using make() function
	// make(map[keyType]valueType)
	ages := make(map[string]int)
	fmt.Println("Empty map:", ages)
	
	// Method 2: Map literal
	scores := map[string]int{
		"Alice":   85,
		"<PERSON>":     92,
		"<PERSON>": 78,
	}
	fmt.Println("Score map:", scores)
	
	fmt.Println()
	
	// ADDING and UPDATING values
	// Use square brackets with key to set value
	ages["Alice"] = 25
	ages["<PERSON>"] = 30
	ages["<PERSON>"] = 22
	
	fmt.Println("Ages after adding:", ages)
	
	// Update existing value
	ages["Alice"] = 26 // Alice had a birthday
	fmt.Println("After <PERSON>'s birthday:", ages)
	
	fmt.Println()
	
	// ACCESSING values
	// Use square brackets with key to get value
	aliceAge := ages["Alice"]
	fmt.Printf("Alice's age: %d\n", aliceAge)
	
	// What happens if key doesn't exist?
	dianaAge := ages["<PERSON>"] // Diana doesn't exist in map
	fmt.Printf("<PERSON>'s age: %d\n", dianaAge) // Returns zero value (0 for int)
	
	// CHECKING if key exists
	// Use two-value assignment to check existence
	age, exists := ages["Diana"]
	if exists {
		fmt.Printf("Diana's age: %d\n", age)
	} else {
		fmt.Println("Diana not found in map")
	}
	
	// Check for existing key
	age, exists = ages["Bob"]
	if exists {
		fmt.Printf("Bob's age: %d\n", age)
	}
	
	fmt.Println()
	
	// DELETING from maps
	// Use delete() function
	fmt.Println("Before deletion:", ages)
	delete(ages, "Charlie")
	fmt.Println("After deleting Charlie:", ages)
	
	// Deleting non-existent key is safe (no error)
	delete(ages, "NonExistent")
	fmt.Println("After deleting non-existent key:", ages)
	
	fmt.Println()
	
	// MAP LENGTH
	fmt.Printf("Number of people in ages map: %d\n", len(ages))
	fmt.Printf("Number of people in scores map: %d\n", len(scores))
	
	fmt.Println()
	
	// ITERATING through maps
	fmt.Println("=== Iterating Through Maps ===")
	
	// Using range to iterate over key-value pairs
	fmt.Println("All ages:")
	for name, age := range ages {
		fmt.Printf("%s is %d years old\n", name, age)
	}
	
	fmt.Println("\nAll scores:")
	for student, score := range scores {
		fmt.Printf("%s scored %d points\n", student, score)
	}
	
	// Iterate over keys only
	fmt.Println("\nJust the names:")
	for name := range ages {
		fmt.Printf("- %s\n", name)
	}
	
	// Iterate over values only (use blank identifier for key)
	fmt.Println("\nJust the ages:")
	for _, age := range ages {
		fmt.Printf("Age: %d\n", age)
	}
	
	fmt.Println()
	
	// MAPS with different types
	fmt.Println("=== Different Map Types ===")
	
	// String to string map
	capitals := map[string]string{
		"USA":    "Washington D.C.",
		"France": "Paris",
		"Japan":  "Tokyo",
		"Brazil": "Brasília",
	}
	
	fmt.Println("World capitals:")
	for country, capital := range capitals {
		fmt.Printf("%s: %s\n", country, capital)
	}
	
	fmt.Println()
	
	// Integer to string map
	weekdays := map[int]string{
		1: "Monday",
		2: "Tuesday",
		3: "Wednesday",
		4: "Thursday",
		5: "Friday",
		6: "Saturday",
		7: "Sunday",
	}
	
	fmt.Printf("Day 3 is: %s\n", weekdays[3])
	fmt.Printf("Day 7 is: %s\n", weekdays[7])
	
	fmt.Println()
	
	// NESTED MAPS (map of maps)
	fmt.Println("=== Nested Maps ===")
	
	// Map of student information (each student has their own map)
	students := map[string]map[string]interface{}{
		"Alice": {
			"age":    25,
			"grade":  "A",
			"active": true,
		},
		"Bob": {
			"age":    30,
			"grade":  "B",
			"active": false,
		},
	}
	
	fmt.Println("Student information:")
	for name, info := range students {
		fmt.Printf("%s:\n", name)
		for key, value := range info {
			fmt.Printf("  %s: %v\n", key, value)
		}
	}
}
