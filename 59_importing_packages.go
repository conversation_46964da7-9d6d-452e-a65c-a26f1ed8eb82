// Package declaration
package main

// DIFFERENT WAYS TO IMPORT PACKAGES

// Standard single imports
import "fmt"
import "time"
import "strings"

// Grouped imports (preferred style)
import (
	"encoding/json"
	"math"
	"net/http"
	"os"
	"strconv"
)

// Import with alias
import (
	"crypto/rand"
	cryptorand "crypto/rand" // Explicit alias
	mathrand "math/rand"     // Alias to avoid naming conflict
)

// Import for side effects only (blank import)
import (
	_ "image/png" // Registers PNG decoder
)

// PACKAGE USAGE EXAMPLES

// Function demonstrating standard library packages
func demonstrateStandardLibrary() {
	fmt.Println("=== Standard Library Packages ===")
	
	// fmt package - formatted I/O
	fmt.Printf("Current time: %v\n", time.Now())
	
	// strings package - string manipulation
	text := "  Hello, Go Packages!  "
	fmt.Printf("Original: '%s'\n", text)
	fmt.Printf("Trimmed: '%s'\n", strings.TrimSpace(text))
	fmt.Printf("Uppercase: '%s'\n", strings.ToUpper(text))
	fmt.Printf("Contains 'Go': %t\n", strings.Contains(text, "Go"))
	
	// strconv package - string conversions
	numStr := "42"
	num, err := strconv.Atoi(numStr)
	if err != nil {
		fmt.Printf("Conversion error: %v\n", err)
	} else {
		fmt.Printf("String '%s' as integer: %d\n", numStr, num)
	}
	
	// math package - mathematical functions
	fmt.Printf("Square root of 16: %.2f\n", math.Sqrt(16))
	fmt.Printf("Pi: %.6f\n", math.Pi)
	fmt.Printf("Max of 10 and 20: %.0f\n", math.Max(10, 20))
	
	fmt.Println()
}

// Function demonstrating JSON package
func demonstrateJSONPackage() {
	fmt.Println("=== JSON Package ===")
	
	// Define a struct for JSON marshaling
	type Person struct {
		Name    string `json:"name"`
		Age     int    `json:"age"`
		Email   string `json:"email"`
		Active  bool   `json:"active"`
	}
	
	// Create a person
	person := Person{
		Name:   "Alice Johnson",
		Age:    30,
		Email:  "<EMAIL>",
		Active: true,
	}
	
	// Marshal to JSON
	jsonData, err := json.Marshal(person)
	if err != nil {
		fmt.Printf("JSON marshal error: %v\n", err)
		return
	}
	
	fmt.Printf("Person as JSON: %s\n", string(jsonData))
	
	// Unmarshal from JSON
	jsonString := `{"name":"Bob Smith","age":25,"email":"<EMAIL>","active":false}`
	var newPerson Person
	
	err = json.Unmarshal([]byte(jsonString), &newPerson)
	if err != nil {
		fmt.Printf("JSON unmarshal error: %v\n", err)
		return
	}
	
	fmt.Printf("Parsed person: %+v\n", newPerson)
	
	// Working with JSON objects (map)
	var data map[string]interface{}
	err = json.Unmarshal([]byte(jsonString), &data)
	if err != nil {
		fmt.Printf("JSON unmarshal to map error: %v\n", err)
		return
	}
	
	fmt.Printf("JSON as map: %v\n", data)
	fmt.Printf("Name from map: %s\n", data["name"])
	
	fmt.Println()
}

// Function demonstrating HTTP package
func demonstrateHTTPPackage() {
	fmt.Println("=== HTTP Package ===")
	
	// Create a simple HTTP handler
	handler := func(w http.ResponseWriter, r *http.Request) {
		response := map[string]interface{}{
			"message":   "Hello from Go HTTP server!",
			"timestamp": time.Now(),
			"method":    r.Method,
			"path":      r.URL.Path,
		}
		
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}
	
	// Register handler
	http.HandleFunc("/api/hello", handler)
	
	// Health check endpoint
	http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})
	
	fmt.Println("HTTP server configured with endpoints:")
	fmt.Println("  - /api/hello")
	fmt.Println("  - /health")
	fmt.Println("Note: To start server, uncomment http.ListenAndServe line")
	
	// Uncomment the next line to actually start the server
	// log.Fatal(http.ListenAndServe(":8080", nil))
	
	fmt.Println()
}

// Function demonstrating file operations
func demonstrateFilePackages() {
	fmt.Println("=== File Operations Packages ===")
	
	// os package - operating system interface
	fmt.Printf("Current working directory: ")
	if cwd, err := os.Getwd(); err == nil {
		fmt.Println(cwd)
	} else {
		fmt.Printf("Error: %v\n", err)
	}
	
	// Environment variables
	fmt.Printf("PATH environment variable length: %d\n", len(os.Getenv("PATH")))
	
	// Create a temporary file
	tempFile := "temp_demo.txt"
	content := "This is a temporary file created by Go!\nIt demonstrates file operations.\n"
	
	err := os.WriteFile(tempFile, []byte(content), 0644)
	if err != nil {
		fmt.Printf("Error writing file: %v\n", err)
	} else {
		fmt.Printf("Created temporary file: %s\n", tempFile)
		
		// Read the file back
		data, err := os.ReadFile(tempFile)
		if err != nil {
			fmt.Printf("Error reading file: %v\n", err)
		} else {
			fmt.Printf("File content:\n%s", string(data))
		}
		
		// Clean up
		os.Remove(tempFile)
		fmt.Printf("Cleaned up temporary file\n")
	}
	
	fmt.Println()
}

// Function demonstrating random number generation
func demonstrateRandomPackages() {
	fmt.Println("=== Random Number Packages ===")
	
	// math/rand package - pseudo-random numbers
	mathrand.Seed(time.Now().UnixNano())
	
	fmt.Println("Math/rand (pseudo-random):")
	fmt.Printf("  Random int: %d\n", mathrand.Int())
	fmt.Printf("  Random int 1-100: %d\n", mathrand.Intn(100)+1)
	fmt.Printf("  Random float64: %.6f\n", mathrand.Float64())
	
	// crypto/rand package - cryptographically secure random
	fmt.Println("Crypto/rand (cryptographically secure):")
	
	// Generate random bytes
	randomBytes := make([]byte, 8)
	_, err := cryptorand.Read(randomBytes)
	if err != nil {
		fmt.Printf("Error generating random bytes: %v\n", err)
	} else {
		fmt.Printf("  Random bytes: %x\n", randomBytes)
	}
	
	fmt.Println()
}

// Function demonstrating time package
func demonstrateTimePackage() {
	fmt.Println("=== Time Package ===")
	
	// Current time
	now := time.Now()
	fmt.Printf("Current time: %v\n", now)
	fmt.Printf("Unix timestamp: %d\n", now.Unix())
	
	// Formatting time
	fmt.Printf("Formatted time: %s\n", now.Format("2006-01-02 15:04:05"))
	fmt.Printf("Custom format: %s\n", now.Format("Jan 2, 2006 at 3:04 PM"))
	
	// Parsing time
	timeStr := "2024-01-01 12:00:00"
	parsedTime, err := time.Parse("2006-01-02 15:04:05", timeStr)
	if err != nil {
		fmt.Printf("Time parsing error: %v\n", err)
	} else {
		fmt.Printf("Parsed time: %v\n", parsedTime)
	}
	
	// Time calculations
	future := now.Add(24 * time.Hour)
	fmt.Printf("24 hours from now: %v\n", future)
	
	duration := future.Sub(now)
	fmt.Printf("Duration until future: %v\n", duration)
	
	// Sleep (commented out to avoid delay)
	fmt.Println("Sleeping for 100ms...")
	time.Sleep(100 * time.Millisecond)
	fmt.Println("Sleep completed")
	
	fmt.Println()
}

// Function showing package organization patterns
func showPackagePatterns() {
	fmt.Println("=== Package Import Patterns ===")
	
	fmt.Println("1. Standard imports:")
	fmt.Println(`   import "fmt"`)
	fmt.Println(`   import "time"`)
	
	fmt.Println("\n2. Grouped imports (preferred):")
	fmt.Println(`   import (`)
	fmt.Println(`       "fmt"`)
	fmt.Println(`       "time"`)
	fmt.Println(`   )`)
	
	fmt.Println("\n3. Aliased imports:")
	fmt.Println(`   import (`)
	fmt.Println(`       "crypto/rand"`)
	fmt.Println(`       mathrand "math/rand"`)
	fmt.Println(`   )`)
	
	fmt.Println("\n4. Blank imports (side effects):")
	fmt.Println(`   import _ "image/png"`)
	
	fmt.Println("\n5. Dot imports (not recommended):")
	fmt.Println(`   import . "fmt"  // Allows Printf instead of fmt.Printf`)
	
	fmt.Println()
}

// Main function
func main() {
	fmt.Println("=== IMPORTING PACKAGES IN GO ===")
	fmt.Println()
	
	// Show import patterns
	showPackagePatterns()
	
	// Demonstrate various standard library packages
	demonstrateStandardLibrary()
	demonstrateJSONPackage()
	demonstrateHTTPPackage()
	demonstrateFilePackages()
	demonstrateRandomPackages()
	demonstrateTimePackage()
	
	// Common standard library packages
	fmt.Println("=== Common Standard Library Packages ===")
	
	packages := []struct {
		name        string
		description string
	}{
		{"fmt", "Formatted I/O (printing, scanning)"},
		{"strings", "String manipulation functions"},
		{"strconv", "String conversions"},
		{"time", "Time and date operations"},
		{"math", "Mathematical functions and constants"},
		{"os", "Operating system interface"},
		{"io", "I/O primitives"},
		{"net/http", "HTTP client and server"},
		{"encoding/json", "JSON encoding and decoding"},
		{"database/sql", "SQL database interface"},
		{"crypto/rand", "Cryptographically secure random numbers"},
		{"regexp", "Regular expressions"},
		{"sort", "Sorting algorithms"},
		{"sync", "Synchronization primitives"},
		{"context", "Request context handling"},
		{"log", "Simple logging"},
		{"flag", "Command-line flag parsing"},
		{"path/filepath", "File path manipulation"},
		{"bufio", "Buffered I/O"},
		{"bytes", "Byte slice operations"},
	}
	
	for _, pkg := range packages {
		fmt.Printf("%-20s - %s\n", pkg.name, pkg.description)
	}
	
	fmt.Println()
	
	// Best practices
	fmt.Println("=== Package Import Best Practices ===")
	fmt.Println("1. Group imports: standard library, third-party, local")
	fmt.Println("2. Use aliases for conflicting package names")
	fmt.Println("3. Avoid dot imports (except in tests)")
	fmt.Println("4. Use blank imports only when necessary")
	fmt.Println("5. Keep imports organized and clean")
	fmt.Println("6. Remove unused imports (gofmt will help)")
	fmt.Println("7. Use meaningful aliases")
	fmt.Println("8. Import only what you need")
	
	fmt.Println()
	
	// Third-party package examples
	fmt.Println("=== Popular Third-Party Packages ===")
	fmt.Println("Web Frameworks:")
	fmt.Println("  - github.com/gin-gonic/gin")
	fmt.Println("  - github.com/gorilla/mux")
	fmt.Println("  - github.com/labstack/echo")
	
	fmt.Println("Database:")
	fmt.Println("  - github.com/lib/pq (PostgreSQL)")
	fmt.Println("  - github.com/go-sql-driver/mysql")
	fmt.Println("  - go.mongodb.org/mongo-driver/mongo")
	
	fmt.Println("Utilities:")
	fmt.Println("  - github.com/sirupsen/logrus (logging)")
	fmt.Println("  - github.com/spf13/cobra (CLI)")
	fmt.Println("  - github.com/stretchr/testify (testing)")
	
	fmt.Println()
	fmt.Println("Package importing demonstration complete! 📚")
	fmt.Println("Key takeaway: Go's package system enables powerful code reuse!")
}
