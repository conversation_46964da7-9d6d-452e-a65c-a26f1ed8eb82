// Test file for calculator package
// Test files must end with _test.go
package calculator

import (
	"math"
	"testing"
)

// BASIC UNIT TESTS

// TestAdd tests the Add function
// Test function names must start with Test
func TestAdd(t *testing.T) {
	// Test basic addition
	result := Add(2, 3)
	expected := 5
	
	if result != expected {
		t.<PERSON><PERSON><PERSON>("Add(2, 3) = %d; expected %d", result, expected)
	}
	
	// Test with negative numbers
	result = Add(-5, 3)
	expected = -2
	
	if result != expected {
		t.<PERSON>("Add(-5, 3) = %d; expected %d", result, expected)
	}
	
	// Test with zero
	result = Add(0, 0)
	expected = 0
	
	if result != expected {
		t.<PERSON><PERSON>("Add(0, 0) = %d; expected %d", result, expected)
	}
}

// TestSubtract tests the Subtract function
func TestSubtract(t *testing.T) {
	testCases := []struct {
		a, b     int
		expected int
	}{
		{5, 3, 2},
		{10, 15, -5},
		{0, 0, 0},
		{-5, -3, -2},
	}
	
	for _, tc := range testCases {
		result := Subtract(tc.a, tc.b)
		if result != tc.expected {
			t.<PERSON><PERSON><PERSON>("Subtract(%d, %d) = %d; expected %d", 
				tc.a, tc.b, result, tc.expected)
		}
	}
}

// TestMultiply tests the Multiply function
func TestMultiply(t *testing.T) {
	tests := map[string]struct {
		a, b     int
		expected int
	}{
		"positive numbers": {3, 4, 12},
		"negative numbers": {-3, -4, 12},
		"mixed signs":      {-3, 4, -12},
		"zero":            {5, 0, 0},
		"one":             {7, 1, 7},
	}
	
	for name, tc := range tests {
		t.Run(name, func(t *testing.T) {
			result := Multiply(tc.a, tc.b)
			if result != tc.expected {
				t.Errorf("Multiply(%d, %d) = %d; expected %d", 
					tc.a, tc.b, result, tc.expected)
			}
		})
	}
}

// TestDivide tests the Divide function (with error handling)
func TestDivide(t *testing.T) {
	// Test successful division
	result, err := Divide(10, 2)
	if err != nil {
		t.Errorf("Divide(10, 2) returned unexpected error: %v", err)
	}
	
	expected := 5.0
	if result != expected {
		t.Errorf("Divide(10, 2) = %f; expected %f", result, expected)
	}
	
	// Test division by zero
	_, err = Divide(10, 0)
	if err == nil {
		t.Error("Divide(10, 0) should return an error")
	}
	
	// Test with floating point result
	result, err = Divide(7, 2)
	if err != nil {
		t.Errorf("Divide(7, 2) returned unexpected error: %v", err)
	}
	
	expected = 3.5
	if result != expected {
		t.Errorf("Divide(7, 2) = %f; expected %f", result, expected)
	}
}

// TestPower tests the Power function
func TestPower(t *testing.T) {
	testCases := []struct {
		base, exp int
		expected  float64
	}{
		{2, 3, 8.0},
		{5, 0, 1.0},
		{10, 2, 100.0},
		{3, 4, 81.0},
	}
	
	for _, tc := range testCases {
		result := Power(tc.base, tc.exp)
		if result != tc.expected {
			t.Errorf("Power(%d, %d) = %f; expected %f", 
				tc.base, tc.exp, result, tc.expected)
		}
	}
}

// TestIsEven tests the IsEven function
func TestIsEven(t *testing.T) {
	evenNumbers := []int{0, 2, 4, 6, 8, 10, -2, -4}
	oddNumbers := []int{1, 3, 5, 7, 9, -1, -3}
	
	// Test even numbers
	for _, num := range evenNumbers {
		if !IsEven(num) {
			t.Errorf("IsEven(%d) = false; expected true", num)
		}
	}
	
	// Test odd numbers
	for _, num := range oddNumbers {
		if IsEven(num) {
			t.Errorf("IsEven(%d) = true; expected false", num)
		}
	}
}

// TestFactorial tests the Factorial function
func TestFactorial(t *testing.T) {
	// Test valid cases
	testCases := []struct {
		input    int
		expected int
	}{
		{0, 1},
		{1, 1},
		{2, 2},
		{3, 6},
		{4, 24},
		{5, 120},
	}
	
	for _, tc := range testCases {
		result, err := Factorial(tc.input)
		if err != nil {
			t.Errorf("Factorial(%d) returned unexpected error: %v", tc.input, err)
		}
		
		if result != tc.expected {
			t.Errorf("Factorial(%d) = %d; expected %d", tc.input, result, tc.expected)
		}
	}
	
	// Test error case
	_, err := Factorial(-1)
	if err == nil {
		t.Error("Factorial(-1) should return an error")
	}
}

// TestMaxMin tests both Max and Min functions
func TestMaxMin(t *testing.T) {
	testCases := []struct {
		a, b         int
		expectedMax  int
		expectedMin  int
	}{
		{5, 3, 5, 3},
		{10, 20, 20, 10},
		{-5, -10, -5, -10},
		{0, 0, 0, 0},
	}
	
	for _, tc := range testCases {
		// Test Max
		maxResult := Max(tc.a, tc.b)
		if maxResult != tc.expectedMax {
			t.Errorf("Max(%d, %d) = %d; expected %d", 
				tc.a, tc.b, maxResult, tc.expectedMax)
		}
		
		// Test Min
		minResult := Min(tc.a, tc.b)
		if minResult != tc.expectedMin {
			t.Errorf("Min(%d, %d) = %d; expected %d", 
				tc.a, tc.b, minResult, tc.expectedMin)
		}
	}
}

// TestAverage tests the Average function
func TestAverage(t *testing.T) {
	// Test normal case
	numbers := []int{1, 2, 3, 4, 5}
	result, err := Average(numbers)
	if err != nil {
		t.Errorf("Average(%v) returned unexpected error: %v", numbers, err)
	}
	
	expected := 3.0
	if result != expected {
		t.Errorf("Average(%v) = %f; expected %f", numbers, result, expected)
	}
	
	// Test empty slice
	emptySlice := []int{}
	_, err = Average(emptySlice)
	if err == nil {
		t.Error("Average of empty slice should return an error")
	}
	
	// Test single element
	singleElement := []int{42}
	result, err = Average(singleElement)
	if err != nil {
		t.Errorf("Average(%v) returned unexpected error: %v", singleElement, err)
	}
	
	expected = 42.0
	if result != expected {
		t.Errorf("Average(%v) = %f; expected %f", singleElement, result, expected)
	}
}

// TestIsPrime tests the IsPrime function
func TestIsPrime(t *testing.T) {
	primes := []int{2, 3, 5, 7, 11, 13, 17, 19, 23, 29}
	nonPrimes := []int{0, 1, 4, 6, 8, 9, 10, 12, 14, 15, 16, 18, 20}
	
	// Test prime numbers
	for _, num := range primes {
		if !IsPrime(num) {
			t.Errorf("IsPrime(%d) = false; expected true", num)
		}
	}
	
	// Test non-prime numbers
	for _, num := range nonPrimes {
		if IsPrime(num) {
			t.Errorf("IsPrime(%d) = true; expected false", num)
		}
	}
}

// TestGCD tests the GCD function
func TestGCD(t *testing.T) {
	testCases := []struct {
		a, b     int
		expected int
	}{
		{12, 8, 4},
		{15, 25, 5},
		{17, 13, 1}, // Coprime numbers
		{0, 5, 5},
		{-12, 8, 4}, // Negative numbers
	}
	
	for _, tc := range testCases {
		result := GCD(tc.a, tc.b)
		if result != tc.expected {
			t.Errorf("GCD(%d, %d) = %d; expected %d", 
				tc.a, tc.b, result, tc.expected)
		}
	}
}

// HELPER FUNCTIONS FOR TESTING

// Helper function to compare floating point numbers
func floatEquals(a, b, tolerance float64) bool {
	return math.Abs(a-b) < tolerance
}

// TestFloatingPointComparison demonstrates floating point testing
func TestFloatingPointComparison(t *testing.T) {
	result := Power(2, 10)
	expected := 1024.0
	tolerance := 0.0001
	
	if !floatEquals(result, expected, tolerance) {
		t.Errorf("Power(2, 10) = %f; expected %f (within tolerance %f)", 
			result, expected, tolerance)
	}
}

// BENCHMARK TESTS (for performance measurement)

// BenchmarkAdd benchmarks the Add function
func BenchmarkAdd(b *testing.B) {
	for i := 0; i < b.N; i++ {
		Add(100, 200)
	}
}

// BenchmarkFactorial benchmarks the Factorial function
func BenchmarkFactorial(b *testing.B) {
	for i := 0; i < b.N; i++ {
		Factorial(10)
	}
}

// BenchmarkIsPrime benchmarks the IsPrime function
func BenchmarkIsPrime(b *testing.B) {
	for i := 0; i < b.N; i++ {
		IsPrime(97) // Test with a prime number
	}
}
