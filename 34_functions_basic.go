// Package declaration
package main

// Import fmt for input/output
import "fmt"

// FUNCTION DECLARATION SYNTAX:
// func functionName(parameter1 type, parameter2 type) returnType {
//     // function body
//     return value
// }

// Simple function with no parameters and no return value
func sayHello() {
	fmt.Println("Hello from a function!")
}

// Function with parameters but no return value
func greet<PERSON>erson(name string, age int) {
	fmt.Printf("Hello %s! You are %d years old.\n", name, age)
}

// Function with parameters and return value
func addNumbers(a int, b int) int {
	result := a + b
	return result
}

// Function with multiple parameters of same type (shorthand)
func multiply(x, y int) int {
	return x * y
}

// Function with multiple return values
func divideWithRemainder(dividend, divisor int) (int, int) {
	quotient := dividend / divisor
	remainder := dividend % divisor
	return quotient, remainder
}

// Function that returns a string
func createGreeting(name string) string {
	greeting := "Welcome, " + name + "!"
	return greeting
}

// Main function
func main() {
	fmt.Println("=== FUNCTION BASICS ===")
	fmt.Println()
	
	// CALLING FUNCTIONS
	
	// Call function with no parameters
	sayHello()
	
	// Call function with parameters
	greetPerson("Alice", 25)
	greetPerson("Bob", 30)
	
	fmt.Println()
	
	// FUNCTIONS WITH RETURN VALUES
	
	// Call function and use return value
	sum := addNumbers(10, 20)
	fmt.Printf("10 + 20 = %d\n", sum)
	
	// Use function return value directly
	fmt.Printf("5 * 7 = %d\n", multiply(5, 7))
	
	// Call function with multiple return values
	quotient, remainder := divideWithRemainder(17, 5)
	fmt.Printf("17 ÷ 5 = %d remainder %d\n", quotient, remainder)
	
	// Ignore one return value using blank identifier
	result, _ := divideWithRemainder(20, 3)
	fmt.Printf("20 ÷ 3 = %d (remainder ignored)\n", result)
	
	fmt.Println()
	
	// USING FUNCTIONS IN EXPRESSIONS
	
	// Use function return in calculations
	total := addNumbers(15, 25) + multiply(3, 4)
	fmt.Printf("(15 + 25) + (3 * 4) = %d\n", total)
	
	// Use function return in conditions
	if addNumbers(5, 5) == 10 {
		fmt.Println("Math works correctly!")
	}
	
	// Store function result in variable
	message := createGreeting("Charlie")
	fmt.Println(message)
	
	fmt.Println()
	
	// PRACTICAL EXAMPLES
	fmt.Println("=== Practical Examples ===")
	
	// Calculate area of rectangles
	length1, width1 := 10, 5
	area1 := calculateArea(length1, width1)
	fmt.Printf("Rectangle 1 (%d x %d): Area = %d\n", length1, width1, area1)
	
	length2, width2 := 8, 6
	area2 := calculateArea(length2, width2)
	fmt.Printf("Rectangle 2 (%d x %d): Area = %d\n", length2, width2, area2)
	
	// Check if numbers are even
	numbers := []int{4, 7, 12, 15, 20}
	fmt.Println("\nChecking if numbers are even:")
	
	for _, num := range numbers {
		if isEven(num) {
			fmt.Printf("%d is even\n", num)
		} else {
			fmt.Printf("%d is odd\n", num)
		}
	}
	
	// Temperature conversion
	fmt.Println("\nTemperature conversions:")
	celsius := []float64{0, 25, 37, 100}
	
	for _, c := range celsius {
		f := celsiusToFahrenheit(c)
		fmt.Printf("%.1f°C = %.1f°F\n", c, f)
	}
}

// Additional function definitions (must be outside main)

// Function to calculate rectangle area
func calculateArea(length, width int) int {
	return length * width
}

// Function to check if number is even
func isEven(number int) bool {
	return number%2 == 0
}

// Function to convert Celsius to Fahrenheit
func celsiusToFahrenheit(celsius float64) float64 {
	fahrenheit := (celsius * 9.0 / 5.0) + 32.0
	return fahrenheit
}
