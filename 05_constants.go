// Package declaration
package main

// Import fmt for printing
import "fmt"

// Constants can be declared outside functions (global constants)
// const keyword tells Go this value will never change
const CompanyName = "TechCorp"

// You can declare multiple constants together
const (
	Pi       = 3.14159
	MaxUsers = 1000
	AppName  = "MyApp"
)

// Main function
func main() {
	// Local constants (inside the function)
	const greeting = "Hello"
	const taxRate = 0.08
	
	// Print the constants
	fmt.Println("Company:", CompanyName)
	fmt.Println("Pi value:", Pi)
	fmt.Println("Max users allowed:", MaxUsers)
	fmt.Println("Application name:", AppName)
	fmt.Println("Greeting:", greeting)
	fmt.Println("Tax rate:", taxRate)
	
	// Example: Calculate price with tax
	var price float64 = 100.0
	var totalPrice = price + (price * taxRate)
	
	fmt.Println("Original price: $", price)
	fmt.Println("Total with tax: $", totalPrice)
	
	// This would cause an error if uncommented:
	// taxRate = 0.10  // Cannot change a constant!
}
