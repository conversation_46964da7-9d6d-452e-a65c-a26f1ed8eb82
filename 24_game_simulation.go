// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// GAME SIMULATION - Demonstrates loops in a real-world scenario
	// Simple RPG-style battle system
	
	// Game setup
	playerName := "Hero"
	playerHealth := 100
	playerLevel := 1
	experience := 0
	experienceToNextLevel := 100
	
	enemyName := "Goblin"
	enemyHealth := 50
	enemyAttack := 15
	
	fmt.Println("=== WELCOME TO ADVENTURE QUEST ===")
	fmt.Printf("Player: %s (Level %d)\n", playerName, playerLevel)
	fmt.Printf("Health: %d/100\n", playerHealth)
	fmt.Println()
	
	// Battle loop
	battleRound := 1
	
	fmt.Printf("A wild %s appears!\n", enemyName)
	fmt.Printf("%s Health: %d\n", enemyName, enemyHealth)
	fmt.Println()
	
	// Continue battle until someone dies
	for playerHealth > 0 && enemyHealth > 0 {
		fmt.Printf("--- Round %d ---\n", battleRound)
		
		// Player attacks (simulate random damage between 20-30)
		playerAttack := 20 + (battleRound % 11) // Simulates 20-30 damage
		enemyHealth -= playerAttack
		
		fmt.Printf("%s attacks for %d damage!\n", playerName, playerAttack)
		
		// Check if enemy is defeated
		if enemyHealth <= 0 {
			fmt.Printf("%s is defeated!\n", enemyName)
			break // Exit battle loop
		} else {
			fmt.Printf("%s health: %d\n", enemyName, enemyHealth)
		}
		
		// Enemy attacks back
		playerHealth -= enemyAttack
		fmt.Printf("%s attacks for %d damage!\n", enemyName, enemyAttack)
		
		// Check if player is defeated
		if playerHealth <= 0 {
			fmt.Printf("%s is defeated! Game Over!\n", playerName)
			break // Exit battle loop
		} else {
			fmt.Printf("%s health: %d\n", playerName, playerHealth)
		}
		
		fmt.Println()
		battleRound++
		
		// Safety check to prevent infinite loop
		if battleRound > 10 {
			fmt.Println("Battle is taking too long! Both fighters retreat.")
			break
		}
	}
	
	// Post-battle results
	fmt.Println()
	fmt.Println("=== BATTLE RESULTS ===")
	
	if playerHealth > 0 {
		// Player won
		experienceGained := 50
		goldGained := 25
		
		fmt.Println("🎉 Victory!")
		fmt.Printf("Experience gained: %d\n", experienceGained)
		fmt.Printf("Gold gained: %d\n", goldGained)
		
		// Add experience
		experience += experienceGained
		
		// Check for level up
		if experience >= experienceToNextLevel {
			playerLevel++
			experience -= experienceToNextLevel
			playerHealth = 100 // Full heal on level up
			
			fmt.Println()
			fmt.Println("🌟 LEVEL UP!")
			fmt.Printf("You are now level %d!\n", playerLevel)
			fmt.Println("Health restored to full!")
		}
		
		// Display final stats
		fmt.Println()
		fmt.Println("=== FINAL STATS ===")
		fmt.Printf("Player: %s\n", playerName)
		fmt.Printf("Level: %d\n", playerLevel)
		fmt.Printf("Health: %d/100\n", playerHealth)
		fmt.Printf("Experience: %d/%d\n", experience, experienceToNextLevel)
		
	} else {
		// Player lost
		fmt.Println("💀 Defeat!")
		fmt.Println("You have been defeated in battle.")
		fmt.Println("Better luck next time!")
	}
	
	fmt.Println()
	
	// Bonus: Show a simple progress bar for experience
	fmt.Println("Experience Progress:")
	fmt.Print("[")
	
	// Calculate how many bars to show (out of 20)
	progressBars := (experience * 20) / experienceToNextLevel
	
	for i := 0; i < 20; i++ {
		if i < progressBars {
			fmt.Print("█") // Filled bar
		} else {
			fmt.Print("░") // Empty bar
		}
	}
	
	fmt.Printf("] %d/%d\n", experience, experienceToNextLevel)
	
	fmt.Println()
	fmt.Println("Thanks for playing Adventure Quest! 🗡️")
}
