// Package declaration
package main

// Import packages
import (
	"bufio"
	"fmt"
	"io"
	"os"
	"strings"
)

// HELPER FUNCTIONS

// Function to create sample files for demonstration
func createSampleFiles() error {
	// Create a simple text file
	simpleContent := `Hello, World!
This is a sample text file.
It contains multiple lines.
Each line has different content.
End of file.`

	if err := os.WriteFile("sample.txt", []byte(simpleContent), 0644); err != nil {
		return fmt.Errorf("failed to create sample.txt: %w", err)
	}

	// Create a CSV file
	csvContent := `name,age,city
Alice,25,New York
Bob,30,Los Angeles
Charlie,35,Chicago
Diana,28,Seattle`

	if err := os.WriteFile("data.csv", []byte(csvContent), 0644); err != nil {
		return fmt.Errorf("failed to create data.csv: %w", err)
	}

	// Create a config file
	configContent := `# Configuration file
server_port=8080
database_host=localhost
database_port=5432
debug_mode=true
max_connections=100`

	if err := os.WriteFile("config.txt", []byte(configContent), 0644); err != nil {
		return fmt.Errorf("failed to create config.txt: %w", err)
	}

	return nil
}

// Function to read entire file at once
func readEntireFile(filename string) error {
	fmt.Printf("=== Reading entire file: %s ===\n", filename)

	// Method 1: Using os.ReadFile (recommended for small files)
	content, err := os.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("failed to read file %s: %w", filename, err)
	}

	fmt.Printf("File size: %d bytes\n", len(content))
	fmt.Printf("Content:\n%s\n", string(content))

	return nil
}

// Function to read file line by line
func readFileLineByLine(filename string) error {
	fmt.Printf("=== Reading file line by line: %s ===\n", filename)

	// Open the file
	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("failed to open file %s: %w", filename, err)
	}
	defer file.Close() // Always close the file

	// Create a scanner to read line by line
	scanner := bufio.NewScanner(file)
	lineNumber := 1

	// Read each line
	for scanner.Scan() {
		line := scanner.Text()
		fmt.Printf("Line %d: %s\n", lineNumber, line)
		lineNumber++
	}

	// Check for scanning errors
	if err := scanner.Err(); err != nil {
		return fmt.Errorf("error reading file %s: %w", filename, err)
	}

	fmt.Printf("Total lines read: %d\n", lineNumber-1)
	return nil
}

// Function to read file with custom buffer size
func readFileWithBuffer(filename string, bufferSize int) error {
	fmt.Printf("=== Reading file with buffer (%d bytes): %s ===\n", bufferSize, filename)

	// Open the file
	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("failed to open file %s: %w", filename, err)
	}
	defer file.Close()

	// Create a buffer
	buffer := make([]byte, bufferSize)
	totalBytesRead := 0

	for {
		// Read into buffer
		bytesRead, err := file.Read(buffer)
		if err != nil {
			if err == io.EOF {
				// End of file reached
				if bytesRead > 0 {
					fmt.Printf("Final chunk (%d bytes): %s\n", bytesRead, string(buffer[:bytesRead]))
					totalBytesRead += bytesRead
				}
				break
			}
			return fmt.Errorf("error reading file %s: %w", filename, err)
		}

		fmt.Printf("Read chunk (%d bytes): %s\n", bytesRead, string(buffer[:bytesRead]))
		totalBytesRead += bytesRead
	}

	fmt.Printf("Total bytes read: %d\n", totalBytesRead)
	return nil
}

// Function to parse CSV file
func parseCSVFile(filename string) error {
	fmt.Printf("=== Parsing CSV file: %s ===\n", filename)

	file, err := os.Open(filename)
	if err != nil {
		return fmt.Errorf("failed to open CSV file %s: %w", filename, err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	lineNumber := 0
	var headers []string

	for scanner.Scan() {
		line := scanner.Text()
		lineNumber++

		// Split by comma (simple CSV parsing)
		fields := strings.Split(line, ",")

		if lineNumber == 1 {
			// First line contains headers
			headers = fields
			fmt.Printf("Headers: %v\n", headers)
			continue
		}

		// Process data rows
		fmt.Printf("Row %d:\n", lineNumber-1)
		for i, field := range fields {
			if i < len(headers) {
				fmt.Printf("  %s: %s\n", headers[i], field)
			}
		}
		fmt.Println()
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("error parsing CSV file %s: %w", filename, err)
	}

	return nil
}

// Function to parse configuration file
func parseConfigFile(filename string) (map[string]string, error) {
	fmt.Printf("=== Parsing config file: %s ===\n", filename)

	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open config file %s: %w", filename, err)
	}
	defer file.Close()

	config := make(map[string]string)
	scanner := bufio.NewScanner(file)
	lineNumber := 0

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		lineNumber++

		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Parse key=value pairs
		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			fmt.Printf("Warning: Invalid config line %d: %s\n", lineNumber, line)
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])
		config[key] = value

		fmt.Printf("Config: %s = %s\n", key, value)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error parsing config file %s: %w", filename, err)
	}

	return config, nil
}

// Function to demonstrate file information
func getFileInfo(filename string) error {
	fmt.Printf("=== File information: %s ===\n", filename)

	// Get file information
	info, err := os.Stat(filename)
	if err != nil {
		return fmt.Errorf("failed to get file info for %s: %w", filename, err)
	}

	fmt.Printf("Name: %s\n", info.Name())
	fmt.Printf("Size: %d bytes\n", info.Size())
	fmt.Printf("Mode: %s\n", info.Mode())
	fmt.Printf("ModTime: %s\n", info.ModTime())
	fmt.Printf("IsDir: %t\n", info.IsDir())

	return nil
}

// Main function
func main() {
	fmt.Println("=== FILE READING IN GO ===")
	fmt.Println()

	// Create sample files for demonstration
	fmt.Println("Creating sample files...")
	if err := createSampleFiles(); err != nil {
		fmt.Printf("❌ Error creating sample files: %v\n", err)
		return
	}
	fmt.Println("✅ Sample files created")
	fmt.Println()

	// READING ENTIRE FILE
	if err := readEntireFile("sample.txt"); err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	}
	fmt.Println()

	// READING FILE LINE BY LINE
	if err := readFileLineByLine("sample.txt"); err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	}
	fmt.Println()

	// READING WITH CUSTOM BUFFER
	if err := readFileWithBuffer("sample.txt", 20); err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	}
	fmt.Println()

	// PARSING CSV FILE
	if err := parseCSVFile("data.csv"); err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	}
	fmt.Println()

	// PARSING CONFIG FILE
	config, err := parseConfigFile("config.txt")
	if err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	} else {
		fmt.Printf("Parsed configuration: %v\n", config)
	}
	fmt.Println()

	// FILE INFORMATION
	files := []string{"sample.txt", "data.csv", "config.txt"}
	for _, filename := range files {
		if err := getFileInfo(filename); err != nil {
			fmt.Printf("❌ Error: %v\n", err)
		}
		fmt.Println()
	}

	// ERROR HANDLING DEMONSTRATION
	fmt.Println("=== Error Handling Demonstration ===")

	// Try to read non-existent file
	if err := readEntireFile("nonexistent.txt"); err != nil {
		fmt.Printf("❌ Expected error: %v\n", err)
	}

	// Try to read directory as file
	if err := readEntireFile("."); err != nil {
		fmt.Printf("❌ Expected error: %v\n", err)
	}

	fmt.Println()

	// BEST PRACTICES
	fmt.Println("=== File Reading Best Practices ===")
	fmt.Println("1. Always check for errors when opening files")
	fmt.Println("2. Use defer to ensure files are closed")
	fmt.Println("3. Choose appropriate reading method based on file size")
	fmt.Println("4. Use os.ReadFile for small files (< 1MB)")
	fmt.Println("5. Use bufio.Scanner for line-by-line reading")
	fmt.Println("6. Use custom buffers for large files")
	fmt.Println("7. Handle io.EOF properly when reading in chunks")
	fmt.Println("8. Validate file existence with os.Stat if needed")

	// Clean up sample files
	fmt.Println("\nCleaning up sample files...")
	filesToClean := []string{"sample.txt", "data.csv", "config.txt"}
	for _, filename := range filesToClean {
		if err := os.Remove(filename); err != nil {
			fmt.Printf("Warning: Could not remove %s: %v\n", filename, err)
		}
	}
	fmt.Println("✅ Cleanup completed")

	fmt.Println()
	fmt.Println("File reading demonstration complete! 📖")
	fmt.Println("Next: Learn about writing files!")
}
