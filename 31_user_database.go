// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// USER DATABASE SYSTEM
	// Demonstrates practical use of maps for storing user information
	
	fmt.Println("=== USER MANAGEMENT SYSTEM ===")
	fmt.Println()
	
	// USER PROFILES
	// Each user has multiple pieces of information
	userProfiles := make(map[string]map[string]interface{})
	
	// Add user profiles
	userProfiles["alice123"] = map[string]interface{}{
		"name":     "<PERSON>",
		"email":    "<EMAIL>",
		"age":      28,
		"premium":  true,
		"posts":    45,
		"followers": 1250,
	}
	
	userProfiles["bob_dev"] = map[string]interface{}{
		"name":     "<PERSON>",
		"email":    "<EMAIL>",
		"age":      32,
		"premium":  false,
		"posts":    23,
		"followers": 890,
	}
	
	userProfiles["charlie_photo"] = map[string]interface{}{
		"name":     "<PERSON>",
		"email":    "<EMAIL>",
		"age":      25,
		"premium":  true,
		"posts":    78,
		"followers": 2100,
	}
	
	// DISPLAY ALL USERS
	fmt.Println("=== All Registered Users ===")
	for username, profile := range userProfiles {
		fmt.Printf("Username: %s\n", username)
		fmt.Printf("  Name: %s\n", profile["name"])
		fmt.Printf("  Email: %s\n", profile["email"])
		fmt.Printf("  Age: %d\n", profile["age"])
		fmt.Printf("  Premium: %t\n", profile["premium"])
		fmt.Printf("  Posts: %d\n", profile["posts"])
		fmt.Printf("  Followers: %d\n", profile["followers"])
		fmt.Println()
	}
	
	// USER AUTHENTICATION
	fmt.Println("=== User Authentication ===")
	
	// Simple password system
	passwords := map[string]string{
		"alice123":     "password123",
		"bob_dev":      "securepass",
		"charlie_photo": "mypassword",
	}
	
	// Simulate login attempt
	loginUsername := "alice123"
	loginPassword := "password123"
	
	// Check if user exists
	if storedPassword, userExists := passwords[loginUsername]; userExists {
		// Check password
		if storedPassword == loginPassword {
			fmt.Printf("✅ Login successful for %s\n", loginUsername)
			
			// Display user info after successful login
			if profile, exists := userProfiles[loginUsername]; exists {
				fmt.Printf("Welcome back, %s!\n", profile["name"])
			}
		} else {
			fmt.Printf("❌ Invalid password for %s\n", loginUsername)
		}
	} else {
		fmt.Printf("❌ User %s not found\n", loginUsername)
	}
	
	fmt.Println()
	
	// USER STATISTICS
	fmt.Println("=== User Statistics ===")
	
	totalUsers := len(userProfiles)
	premiumUsers := 0
	totalPosts := 0
	totalFollowers := 0
	
	// Calculate statistics
	for _, profile := range userProfiles {
		if profile["premium"].(bool) {
			premiumUsers++
		}
		totalPosts += profile["posts"].(int)
		totalFollowers += profile["followers"].(int)
	}
	
	fmt.Printf("Total Users: %d\n", totalUsers)
	fmt.Printf("Premium Users: %d (%.1f%%)\n", premiumUsers, 
		float64(premiumUsers)/float64(totalUsers)*100)
	fmt.Printf("Total Posts: %d\n", totalPosts)
	fmt.Printf("Total Followers: %d\n", totalFollowers)
	fmt.Printf("Average Posts per User: %.1f\n", 
		float64(totalPosts)/float64(totalUsers))
	fmt.Printf("Average Followers per User: %.1f\n", 
		float64(totalFollowers)/float64(totalUsers))
	
	fmt.Println()
	
	// CONTENT MANAGEMENT
	fmt.Println("=== Content Management ===")
	
	// Track posts by category
	postsByCategory := map[string]int{
		"Technology": 15,
		"Travel":     12,
		"Food":       8,
		"Sports":     10,
		"Music":      6,
	}
	
	fmt.Println("Posts by category:")
	for category, count := range postsByCategory {
		fmt.Printf("  %s: %d posts\n", category, count)
	}
	
	// Find most popular category
	maxPosts := 0
	popularCategory := ""
	
	for category, count := range postsByCategory {
		if count > maxPosts {
			maxPosts = count
			popularCategory = category
		}
	}
	
	fmt.Printf("\nMost popular category: %s (%d posts)\n", 
		popularCategory, maxPosts)
	
	fmt.Println()
	
	// USER ACTIVITY TRACKING
	fmt.Println("=== User Activity Tracking ===")
	
	// Track last login times (simplified as days ago)
	lastLogin := map[string]int{
		"alice123":     1, // 1 day ago
		"bob_dev":      3, // 3 days ago
		"charlie_photo": 0, // today
	}
	
	fmt.Println("User activity:")
	for username, daysAgo := range lastLogin {
		if profile, exists := userProfiles[username]; exists {
			name := profile["name"].(string)
			
			if daysAgo == 0 {
				fmt.Printf("  %s (%s): Active today ✅\n", name, username)
			} else if daysAgo == 1 {
				fmt.Printf("  %s (%s): Last seen 1 day ago\n", name, username)
			} else {
				fmt.Printf("  %s (%s): Last seen %d days ago\n", name, username, daysAgo)
			}
		}
	}
	
	// Find inactive users (more than 2 days)
	fmt.Println("\nInactive users (>2 days):")
	for username, daysAgo := range lastLogin {
		if daysAgo > 2 {
			if profile, exists := userProfiles[username]; exists {
				name := profile["name"].(string)
				fmt.Printf("  %s (%s): %d days inactive\n", name, username, daysAgo)
			}
		}
	}
	
	fmt.Println()
	fmt.Println("User management system complete! 👥")
}
