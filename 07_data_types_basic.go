// Package declaration
package main

// Import fmt for printing
import "fmt"

// Main function
func main() {
	// INTEGER TYPES (whole numbers)
	// int can be positive, negative, or zero
	var age int = 25
	var temperature int = -5
	var score int = 0
	
	// FLOAT TYPES (decimal numbers)
	// float64 is for numbers with decimal points
	var price float64 = 19.99
	var weight float64 = 68.5
	var pi float64 = 3.14159
	
	// STRING TYPE (text)
	// string is for any text, put in double quotes
	var name string = "Alice"
	var city string = "London"
	var message string = "Hello, World!"
	
	// BOOLEAN TYPE (true or false)
	// bool can only be true or false
	var isStudent bool = true
	var isRaining bool = false
	var hasLicense bool = true
	
	// Print all the variables with their types
	fmt.Println("=== INTEGER VALUES ===")
	fmt.Println("Age:", age)
	fmt.Println("Temperature:", temperature)
	fmt.Println("Score:", score)
	
	fmt.Println("\n=== FLOAT VALUES ===")
	fmt.Println("Price: $", price)
	fmt.Println("Weight:", weight, "kg")
	fmt.Println("Pi:", pi)
	
	fmt.Println("\n=== STRING VALUES ===")
	fmt.Println("Name:", name)
	fmt.Println("City:", city)
	fmt.Println("Message:", message)
	
	fmt.Println("\n=== BOOLEAN VALUES ===")
	fmt.Println("Is student:", isStudent)
	fmt.Println("Is raining:", isRaining)
	fmt.Println("Has license:", hasLicense)
}
