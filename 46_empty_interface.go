// Package declaration
package main

// Import fmt for input/output
import "fmt"

// EMPTY INTERFACE EXAMPLES

// Function that accepts any type using empty interface
func printAnything(value interface{}) {
	fmt.Printf("Value: %v, Type: %T\n", value, value)
}

// Function that processes different types
func processValue(value interface{}) {
	// Use type assertion to check what type it actually is
	switch v := value.(type) {
	case int:
		fmt.Printf("Integer: %d (doubled: %d)\n", v, v*2)
	case string:
		fmt.Printf("String: %s (length: %d)\n", v, len(v))
	case float64:
		fmt.Printf("Float: %.2f (squared: %.2f)\n", v, v*v)
	case bool:
		fmt.Printf("Boolean: %t (negated: %t)\n", v, !v)
	case []int:
		fmt.Printf("Integer slice: %v (sum: %d)\n", v, sumSlice(v))
	default:
		fmt.Printf("Unknown type: %T with value %v\n", v, v)
	}
}

// Helper function to sum a slice
func sumSlice(slice []int) int {
	sum := 0
	for _, num := range slice {
		sum += num
	}
	return sum
}

// Function demonstrating type assertion with ok pattern
func safeTypeAssertion(value interface{}) {
	// Safe type assertion - returns value and boolean
	if str, ok := value.(string); ok {
		fmt.Printf("✅ Successfully converted to string: %s\n", str)
	} else {
		fmt.Printf("❌ Cannot convert %v to string (type: %T)\n", value, value)
	}
}

// Function that works with slice of any type
func printSlice(slice interface{}) {
	fmt.Printf("Slice: %v (Type: %T)\n", slice, slice)
	
	// Use reflection-like approach with type assertion
	switch s := slice.(type) {
	case []int:
		fmt.Printf("  Integer slice with %d elements\n", len(s))
		for i, v := range s {
			fmt.Printf("  [%d]: %d\n", i, v)
		}
	case []string:
		fmt.Printf("  String slice with %d elements\n", len(s))
		for i, v := range s {
			fmt.Printf("  [%d]: %s\n", i, v)
		}
	case []float64:
		fmt.Printf("  Float slice with %d elements\n", len(s))
		for i, v := range s {
			fmt.Printf("  [%d]: %.2f\n", i, v)
		}
	default:
		fmt.Printf("  Unsupported slice type: %T\n", s)
	}
}

// Struct for demonstration
type Person struct {
	Name string
	Age  int
}

// Method on Person
func (p Person) String() string {
	return fmt.Sprintf("Person{Name: %s, Age: %d}", p.Name, p.Age)
}

// Function that creates a map with mixed types
func createMixedMap() map[string]interface{} {
	return map[string]interface{}{
		"name":      "Alice",
		"age":       30,
		"salary":    75000.50,
		"active":    true,
		"skills":    []string{"Go", "Python", "JavaScript"},
		"person":    Person{Name: "Bob", Age: 25},
		"numbers":   []int{1, 2, 3, 4, 5},
	}
}

// Function to process mixed map
func processMixedMap(data map[string]interface{}) {
	fmt.Println("Processing mixed data map:")
	
	for key, value := range data {
		fmt.Printf("\nKey: %s\n", key)
		
		switch v := value.(type) {
		case string:
			fmt.Printf("  String value: %s\n", v)
		case int:
			fmt.Printf("  Integer value: %d\n", v)
		case float64:
			fmt.Printf("  Float value: %.2f\n", v)
		case bool:
			fmt.Printf("  Boolean value: %t\n", v)
		case []string:
			fmt.Printf("  String slice: %v\n", v)
		case []int:
			fmt.Printf("  Integer slice: %v\n", v)
		case Person:
			fmt.Printf("  Person: %s\n", v)
		default:
			fmt.Printf("  Other type (%T): %v\n", v, v)
		}
	}
}

// Main function
func main() {
	fmt.Println("=== EMPTY INTERFACE & TYPE ASSERTION ===")
	fmt.Println()
	
	// BASIC EMPTY INTERFACE USAGE
	fmt.Println("=== Basic Empty Interface ===")
	
	// Empty interface can hold any type
	var anything interface{}
	
	anything = 42
	printAnything(anything)
	
	anything = "Hello, World!"
	printAnything(anything)
	
	anything = 3.14159
	printAnything(anything)
	
	anything = true
	printAnything(anything)
	
	anything = []int{1, 2, 3, 4, 5}
	printAnything(anything)
	
	anything = Person{Name: "Alice", Age: 30}
	printAnything(anything)
	
	fmt.Println()
	
	// TYPE SWITCH
	fmt.Println("=== Type Switch Examples ===")
	
	values := []interface{}{
		42,
		"Go Programming",
		3.14159,
		true,
		[]int{10, 20, 30},
		Person{Name: "Bob", Age: 25},
		map[string]int{"a": 1, "b": 2},
	}
	
	for i, value := range values {
		fmt.Printf("\nValue %d:\n", i+1)
		processValue(value)
	}
	
	fmt.Println()
	
	// SAFE TYPE ASSERTION
	fmt.Println("=== Safe Type Assertion ===")
	
	testValues := []interface{}{
		"This is a string",
		42,
		3.14,
		true,
	}
	
	for _, value := range testValues {
		safeTypeAssertion(value)
	}
	
	fmt.Println()
	
	// UNSAFE TYPE ASSERTION (commented to prevent panic)
	fmt.Println("=== Unsafe Type Assertion (Demonstration) ===")
	
	var value interface{} = 42
	
	// Safe way
	if str, ok := value.(string); ok {
		fmt.Printf("Safe: Got string: %s\n", str)
	} else {
		fmt.Printf("Safe: Value is not a string, it's %T\n", value)
	}
	
	// Unsafe way (would panic if uncommented)
	// str := value.(string) // This would panic!
	// fmt.Printf("Unsafe: Got string: %s\n", str)
	
	fmt.Println("Note: Unsafe type assertion would panic if types don't match")
	
	fmt.Println()
	
	// WORKING WITH SLICES
	fmt.Println("=== Working with Different Slice Types ===")
	
	slices := []interface{}{
		[]int{1, 2, 3, 4, 5},
		[]string{"apple", "banana", "cherry"},
		[]float64{1.1, 2.2, 3.3},
		[]bool{true, false, true}, // This won't be handled
	}
	
	for i, slice := range slices {
		fmt.Printf("\nSlice %d:\n", i+1)
		printSlice(slice)
	}
	
	fmt.Println()
	
	// MIXED DATA MAP
	fmt.Println("=== Mixed Data Map ===")
	
	mixedData := createMixedMap()
	processMixedMap(mixedData)
	
	fmt.Println()
	
	// PRACTICAL EXAMPLE: JSON-like Data Processing
	fmt.Println("=== JSON-like Data Processing ===")
	
	// Simulate JSON data (this is how JSON unmarshaling works)
	jsonLikeData := map[string]interface{}{
		"user": map[string]interface{}{
			"id":    123,
			"name":  "John Doe",
			"email": "<EMAIL>",
			"active": true,
		},
		"orders": []interface{}{
			map[string]interface{}{
				"id":     1001,
				"total":  99.99,
				"items":  []string{"laptop", "mouse"},
			},
			map[string]interface{}{
				"id":     1002,
				"total":  49.99,
				"items":  []string{"book", "pen"},
			},
		},
		"metadata": map[string]interface{}{
			"version":   "1.0",
			"timestamp": "2024-01-01T00:00:00Z",
		},
	}
	
	// Process nested data
	if user, ok := jsonLikeData["user"].(map[string]interface{}); ok {
		fmt.Println("User Information:")
		if name, ok := user["name"].(string); ok {
			fmt.Printf("  Name: %s\n", name)
		}
		if id, ok := user["id"].(int); ok {
			fmt.Printf("  ID: %d\n", id)
		}
		if active, ok := user["active"].(bool); ok {
			fmt.Printf("  Active: %t\n", active)
		}
	}
	
	if orders, ok := jsonLikeData["orders"].([]interface{}); ok {
		fmt.Printf("\nOrders (%d total):\n", len(orders))
		for i, order := range orders {
			if orderMap, ok := order.(map[string]interface{}); ok {
				fmt.Printf("  Order %d:\n", i+1)
				if id, ok := orderMap["id"].(int); ok {
					fmt.Printf("    ID: %d\n", id)
				}
				if total, ok := orderMap["total"].(float64); ok {
					fmt.Printf("    Total: $%.2f\n", total)
				}
			}
		}
	}
	
	fmt.Println()
	
	// INTERFACE SLICE OPERATIONS
	fmt.Println("=== Interface Slice Operations ===")
	
	// Create slice of different types
	mixedSlice := []interface{}{
		"Hello",
		42,
		3.14,
		true,
		[]int{1, 2, 3},
		Person{Name: "Charlie", Age: 35},
	}
	
	fmt.Println("Mixed slice contents:")
	for i, item := range mixedSlice {
		fmt.Printf("  [%d] %T: %v\n", i, item, item)
	}
	
	// Filter by type
	fmt.Println("\nFiltering strings from mixed slice:")
	for i, item := range mixedSlice {
		if str, ok := item.(string); ok {
			fmt.Printf("  Found string at index %d: %s\n", i, str)
		}
	}
	
	fmt.Println("\nFiltering numbers from mixed slice:")
	for i, item := range mixedSlice {
		switch v := item.(type) {
		case int:
			fmt.Printf("  Found int at index %d: %d\n", i, v)
		case float64:
			fmt.Printf("  Found float64 at index %d: %.2f\n", i, v)
		}
	}
	
	fmt.Println()
	fmt.Println("Empty interface and type assertion demonstration complete! 📦")
}
