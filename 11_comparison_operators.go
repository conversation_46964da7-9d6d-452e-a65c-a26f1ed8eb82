// Package declaration
package main

// Import fmt for printing
import "fmt"

// Main function
func main() {
	// COMPARISON OPERATORS
	// These operators compare values and return true or false
	
	// Numbers to compare
	age := 18
	minimumAge := 21
	score := 85
	passingScore := 60
	
	// Equal to operator (==)
	// Checks if two values are exactly the same
	isExactAge := age == minimumAge
	fmt.Println("Is age exactly", minimumAge, "?", isExactAge)
	
	// Not equal to operator (!=)
	// Checks if two values are different
	isDifferentAge := age != minimumAge
	fmt.Println("Is age different from", minimumAge, "?", isDifferentAge)
	
	// Less than operator (<)
	// Checks if first value is smaller than second
	isTooYoung := age < minimumAge
	fmt.Println("Is age less than", minimumAge, "?", isTooYoung)
	
	// Greater than operator (>)
	// Checks if first value is bigger than second
	isPassing := score > passingScore
	fmt.Println("Is score greater than", passingScore, "?", isPassing)
	
	// Less than or equal to operator (<=)
	// Checks if first value is smaller than or equal to second
	isEligible := age <= minimumAge
	fmt.Println("Is age less than or equal to", minimumAge, "?", isEligible)
	
	// Greater than or equal to operator (>=)
	// Checks if first value is bigger than or equal to second
	canPass := score >= passingScore
	fmt.Println("Can pass with score", score, "?", canPass)
	
	fmt.Println()
	
	// Comparing strings
	name1 := "Alice"
	name2 := "Bob"
	name3 := "Alice"
	
	fmt.Println("Comparing strings:")
	fmt.Println(name1, "==", name2, ":", name1 == name2)
	fmt.Println(name1, "==", name3, ":", name1 == name3)
	fmt.Println(name1, "!=", name2, ":", name1 != name2)
	
	fmt.Println()
	
	// Practical example: Age verification
	userAge := 25
	drivingAge := 16
	votingAge := 18
	drinkingAge := 21
	
	fmt.Println("=== Age Verification System ===")
	fmt.Println("User age:", userAge)
	fmt.Println("Can drive:", userAge >= drivingAge)
	fmt.Println("Can vote:", userAge >= votingAge)
	fmt.Println("Can drink:", userAge >= drinkingAge)
}
