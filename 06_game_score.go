// Package declaration
package main

// Import fmt for printing
import "fmt"

// Game constants
const (
	GameName    = "Space Adventure"
	MaxLives    = 3
	BonusPoints = 500
)

// Main function
func main() {
	// Player variables (these can change during the game)
	playerName := "Player1"
	currentScore := 0
	livesRemaining := MaxLives
	level := 1
	
	// Display game start information
	fmt.Println("=== Welcome to", GameName, "===")
	fmt.Println("Player:", playerName)
	fmt.Println("Starting lives:", livesRemaining)
	fmt.Println("Current level:", level)
	fmt.Println("Current score:", currentScore)
	fmt.Println()
	
	// Simulate some game events
	fmt.Println("--- Game Events ---")
	
	// Player defeats an enemy
	currentScore = currentScore + 100
	fmt.Println("Enemy defeated! Score:", currentScore)
	
	// Player finds a bonus
	currentScore = currentScore + BonusPoints
	fmt.Println("Bonus found! Score:", currentScore)
	
	// Player loses a life
	livesRemaining = livesRemaining - 1
	fmt.Println("Ouch! Lives remaining:", livesRemaining)
	
	// Player advances to next level
	level = level + 1
	fmt.Println("Level up! Current level:", level)
	
	// Final game status
	fmt.Println()
	fmt.Println("=== Current Game Status ===")
	fmt.Println("Player:", playerName)
	fmt.Println("Score:", currentScore)
	fmt.Println("Level:", level)
	fmt.Println("Lives:", livesRemaining)
}
