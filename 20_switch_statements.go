// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// SWITCH STATEMENTS - Choose from multiple options
	// Better than long if-else chains when checking one variable
	
	// Basic switch statement
	day := 3
	
	// switch keyword followed by the variable to check
	// case keyword for each possible value
	switch day {
	case 1:
		fmt.Println("Monday - Start of work week")
	case 2:
		fmt.Println("Tuesday - Getting into the groove")
	case 3:
		fmt.Println("Wednesday - Hump day!")
	case 4:
		fmt.Println("Thursday - Almost there")
	case 5:
		fmt.Println("Friday - TGIF!")
	case 6:
		fmt.Println("Saturday - Weekend!")
	case 7:
		fmt.Println("Sunday - Rest day")
	default:
		// default runs if no case matches
		fmt.Println("Invalid day number")
	}
	
	// Switch with strings
	grade := "B"
	
	switch grade {
	case "A":
		fmt.Println("Excellent! Grade: A")
	case "B":
		fmt.Println("Good job! Grade: B")
	case "C":
		fmt.Println("Average. Grade: C")
	case "D":
		fmt.Println("Below average. Grade: D")
	case "F":
		fmt.Println("Failed. Grade: F")
	default:
		fmt.Println("Invalid grade")
	}
	
	// Switch with multiple values per case
	month := "June"
	
	switch month {
	case "December", "January", "February":
		fmt.Println("Winter season")
	case "March", "April", "May":
		fmt.Println("Spring season")
	case "June", "July", "August":
		fmt.Println("Summer season")
	case "September", "October", "November":
		fmt.Println("Fall season")
	default:
		fmt.Println("Invalid month")
	}
	
	// Switch without expression (like if-else chain)
	score := 92
	
	// When you don't specify a variable, you can use conditions
	switch {
	case score >= 90:
		fmt.Println("Grade: A (Excellent)")
	case score >= 80:
		fmt.Println("Grade: B (Good)")
	case score >= 70:
		fmt.Println("Grade: C (Average)")
	case score >= 60:
		fmt.Println("Grade: D (Below Average)")
	default:
		fmt.Println("Grade: F (Failed)")
	}
	
	// Switch with fallthrough (rare, but useful to know)
	number := 2
	
	fmt.Print("Number 2 is: ")
	switch number {
	case 2:
		fmt.Print("even, ")
		fallthrough // Continue to next case
	case 1, 3, 5, 7:
		fmt.Print("prime, ")
		fallthrough
	default:
		fmt.Println("a positive integer")
	}
}
