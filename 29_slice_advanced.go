// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// ADVANCED SLICE OPERATIONS
	// Demonstrates more complex slice manipulations
	
	fmt.Println("=== ADVANCED SLICE OPERATIONS ===")
	fmt.Println()
	
	// SLICE CAPACITY vs LENGTH
	fmt.Println("=== Understanding Capacity ===")
	
	// Create slice with specific length and capacity
	slice1 := make([]int, 3, 10) // length=3, capacity=10
	fmt.Printf("slice1: %v\n", slice1)
	fmt.Printf("Length: %d, Capacity: %d\n", len(slice1), cap(slice1))
	
	// Add elements - no reallocation needed until capacity is exceeded
	slice1 = append(slice1, 4, 5, 6, 7, 8, 9, 10)
	fmt.Printf("After append: %v\n", slice1)
	fmt.Printf("Length: %d, Capacity: %d\n", len(slice1), cap(slice1))
	
	// Add one more - this will cause reallocation
	slice1 = append(slice1, 11)
	fmt.Printf("After exceeding capacity: %v\n", slice1)
	fmt.Printf("Length: %d, Capacity: %d\n", len(slice1), cap(slice1))
	
	fmt.Println()
	
	// FILTERING SLICES
	fmt.Println("=== Filtering Slices ===")
	numbers := []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
	fmt.Println("Original numbers:", numbers)
	
	// Filter even numbers
	var evenNumbers []int
	for _, num := range numbers {
		if num%2 == 0 {
			evenNumbers = append(evenNumbers, num)
		}
	}
	fmt.Println("Even numbers:", evenNumbers)
	
	// Filter numbers greater than 5
	var largeNumbers []int
	for _, num := range numbers {
		if num > 5 {
			largeNumbers = append(largeNumbers, num)
		}
	}
	fmt.Println("Numbers > 5:", largeNumbers)
	
	fmt.Println()
	
	// SEARCHING IN SLICES
	fmt.Println("=== Searching in Slices ===")
	names := []string{"Alice", "Bob", "Charlie", "Diana", "Eve"}
	searchName := "Charlie"
	
	// Linear search
	found := false
	foundIndex := -1
	
	for i, name := range names {
		if name == searchName {
			found = true
			foundIndex = i
			break
		}
	}
	
	if found {
		fmt.Printf("Found '%s' at index %d\n", searchName, foundIndex)
	} else {
		fmt.Printf("'%s' not found\n", searchName)
	}
	
	fmt.Println()
	
	// SORTING SLICES (manual bubble sort for demonstration)
	fmt.Println("=== Sorting Slices ===")
	scores := []int{85, 92, 78, 96, 88, 73, 91}
	fmt.Println("Original scores:", scores)
	
	// Bubble sort implementation
	n := len(scores)
	for i := 0; i < n-1; i++ {
		for j := 0; j < n-i-1; j++ {
			if scores[j] > scores[j+1] {
				// Swap elements
				scores[j], scores[j+1] = scores[j+1], scores[j]
			}
		}
	}
	
	fmt.Println("Sorted scores:", scores)
	
	fmt.Println()
	
	// SLICE OF SLICES (2D slice)
	fmt.Println("=== 2D Slices ===")
	
	// Create a 2D slice (slice of slices)
	matrix := [][]int{
		{1, 2, 3},
		{4, 5, 6},
		{7, 8, 9},
	}
	
	fmt.Println("Matrix:")
	for i, row := range matrix {
		fmt.Printf("Row %d: %v\n", i, row)
	}
	
	// Add a new row
	newRow := []int{10, 11, 12}
	matrix = append(matrix, newRow)
	
	fmt.Println("\nAfter adding new row:")
	for i, row := range matrix {
		fmt.Printf("Row %d: %v\n", i, row)
	}
	
	fmt.Println()
	
	// PRACTICAL EXAMPLE: Student Groups
	fmt.Println("=== Student Groups Management ===")
	
	// Each group is a slice of student names
	groups := [][]string{
		{"Alice", "Bob"},
		{"Charlie", "Diana", "Eve"},
		{"Frank", "Grace"},
	}
	
	fmt.Println("Initial groups:")
	for i, group := range groups {
		fmt.Printf("Group %d: %v (%d students)\n", i+1, group, len(group))
	}
	
	// Add student to group 2
	groups[1] = append(groups[1], "Henry")
	
	// Create new group
	newGroup := []string{"Ivy", "Jack"}
	groups = append(groups, newGroup)
	
	fmt.Println("\nAfter modifications:")
	for i, group := range groups {
		fmt.Printf("Group %d: %v (%d students)\n", i+1, group, len(group))
	}
	
	// Calculate total students
	totalStudents := 0
	for _, group := range groups {
		totalStudents += len(group)
	}
	
	fmt.Printf("\nTotal students: %d\n", totalStudents)
	fmt.Printf("Number of groups: %d\n", len(groups))
	fmt.Printf("Average group size: %.1f\n", float64(totalStudents)/float64(len(groups)))
}
