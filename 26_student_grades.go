// Package declaration
package main

// Import fmt for input/output
import "fmt"

// Main function
func main() {
	// STUDENT GRADE MANAGEMENT SYSTEM
	// Demonstrates practical use of arrays
	
	// Student information
	studentNames := [5]string{"<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"}
	mathGrades := [5]int{85, 92, 78, 96, 88}
	scienceGrades := [5]int{90, 87, 82, 94, 91}
	englishGrades := [5]int{88, 85, 90, 89, 93}
	
	fmt.Println("=== STUDENT GRADE REPORT ===")
	fmt.Println()
	
	// Display individual student reports
	for i := 0; i < len(studentNames); i++ {
		fmt.Printf("Student: %s\n", studentNames[i])
		fmt.Printf("Math: %d\n", mathGrades[i])
		fmt.Printf("Science: %d\n", scienceGrades[i])
		fmt.Printf("English: %d\n", englishGrades[i])
		
		// Calculate student's average
		total := mathGrades[i] + scienceGrades[i] + englishGrades[i]
		average := float64(total) / 3.0
		
		fmt.Printf("Average: %.1f\n", average)
		
		// Determine letter grade
		var letterGrade string
		switch {
		case average >= 90:
			letterGrade = "A"
		case average >= 80:
			letterGrade = "B"
		case average >= 70:
			letterGrade = "C"
		case average >= 60:
			letterGrade = "D"
		default:
			letterGrade = "F"
		}
		
		fmt.Printf("Letter Grade: %s\n", letterGrade)
		fmt.Println("---")
	}
	
	fmt.Println()
	
	// CLASS STATISTICS
	fmt.Println("=== CLASS STATISTICS ===")
	
	// Calculate subject averages
	subjects := [3]string{"Math", "Science", "English"}
	subjectGrades := [3][5]int{mathGrades, scienceGrades, englishGrades}
	
	for subjectIndex := 0; subjectIndex < 3; subjectIndex++ {
		subjectName := subjects[subjectIndex]
		grades := subjectGrades[subjectIndex]
		
		// Calculate total and average for this subject
		total := 0
		highest := grades[0]
		lowest := grades[0]
		
		for i := 0; i < len(grades); i++ {
			total += grades[i]
			
			// Find highest grade
			if grades[i] > highest {
				highest = grades[i]
			}
			
			// Find lowest grade
			if grades[i] < lowest {
				lowest = grades[i]
			}
		}
		
		average := float64(total) / float64(len(grades))
		
		fmt.Printf("%s Subject:\n", subjectName)
		fmt.Printf("  Class Average: %.1f\n", average)
		fmt.Printf("  Highest Grade: %d\n", highest)
		fmt.Printf("  Lowest Grade: %d\n", lowest)
		fmt.Println()
	}
	
	// GRADE DISTRIBUTION
	fmt.Println("=== GRADE DISTRIBUTION ===")
	
	// Count grades in different ranges
	gradeRanges := [4]string{"90-100 (A)", "80-89 (B)", "70-79 (C)", "Below 70"}
	gradeCounts := [4]int{0, 0, 0, 0} // Initialize all counts to 0
	
	// Check all grades from all subjects
	allGrades := [15]int{} // 5 students × 3 subjects = 15 grades
	gradeIndex := 0
	
	// Collect all grades into one array
	for student := 0; student < 5; student++ {
		allGrades[gradeIndex] = mathGrades[student]
		gradeIndex++
		allGrades[gradeIndex] = scienceGrades[student]
		gradeIndex++
		allGrades[gradeIndex] = englishGrades[student]
		gradeIndex++
	}
	
	// Count grades in each range
	for i := 0; i < len(allGrades); i++ {
		grade := allGrades[i]
		
		if grade >= 90 {
			gradeCounts[0]++
		} else if grade >= 80 {
			gradeCounts[1]++
		} else if grade >= 70 {
			gradeCounts[2]++
		} else {
			gradeCounts[3]++
		}
	}
	
	// Display distribution
	for i := 0; i < len(gradeRanges); i++ {
		fmt.Printf("%s: %d grades\n", gradeRanges[i], gradeCounts[i])
	}
	
	fmt.Println()
	
	// HONOR ROLL (students with all A's)
	fmt.Println("=== HONOR ROLL ===")
	fmt.Println("Students with all grades 90 or above:")
	
	for i := 0; i < len(studentNames); i++ {
		// Check if all three grades are 90 or above
		if mathGrades[i] >= 90 && scienceGrades[i] >= 90 && englishGrades[i] >= 90 {
			fmt.Printf("🌟 %s\n", studentNames[i])
		}
	}
}
