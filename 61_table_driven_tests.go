// Package validation provides input validation functions
// This demonstrates table-driven testing patterns
package validation

import (
	"errors"
	"regexp"
	"strings"
	"unicode"
)

// ValidateEmail checks if an email address is valid
func ValidateEmail(email string) error {
	if email == "" {
		return errors.New("email cannot be empty")
	}
	
	// Simple email regex pattern
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	
	if !emailRegex.MatchString(email) {
		return errors.New("invalid email format")
	}
	
	return nil
}

// ValidatePassword checks if a password meets security requirements
func ValidatePassword(password string) error {
	if len(password) < 8 {
		return errors.New("password must be at least 8 characters long")
	}
	
	if len(password) > 128 {
		return errors.New("password must be less than 128 characters long")
	}
	
	hasUpper := false
	hasLower := false
	hasDigit := false
	hasSpecial := false
	
	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsDigit(char):
			hasDigit = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}
	
	if !hasUpper {
		return errors.New("password must contain at least one uppercase letter")
	}
	
	if !hasLower {
		return errors.New("password must contain at least one lowercase letter")
	}
	
	if !hasDigit {
		return errors.New("password must contain at least one digit")
	}
	
	if !hasSpecial {
		return errors.New("password must contain at least one special character")
	}
	
	return nil
}

// ValidateAge checks if an age is within valid range
func ValidateAge(age int) error {
	if age < 0 {
		return errors.New("age cannot be negative")
	}
	
	if age > 150 {
		return errors.New("age cannot be greater than 150")
	}
	
	return nil
}

// ValidateUsername checks if a username is valid
func ValidateUsername(username string) error {
	if len(username) < 3 {
		return errors.New("username must be at least 3 characters long")
	}
	
	if len(username) > 20 {
		return errors.New("username must be less than 20 characters long")
	}
	
	// Username can only contain letters, numbers, and underscores
	for _, char := range username {
		if !unicode.IsLetter(char) && !unicode.IsDigit(char) && char != '_' {
			return errors.New("username can only contain letters, numbers, and underscores")
		}
	}
	
	// Username cannot start with a number
	if unicode.IsDigit(rune(username[0])) {
		return errors.New("username cannot start with a number")
	}
	
	return nil
}

// ValidatePhoneNumber checks if a phone number is valid (simple US format)
func ValidatePhoneNumber(phone string) error {
	// Remove common separators
	cleaned := strings.ReplaceAll(phone, "-", "")
	cleaned = strings.ReplaceAll(cleaned, " ", "")
	cleaned = strings.ReplaceAll(cleaned, "(", "")
	cleaned = strings.ReplaceAll(cleaned, ")", "")
	cleaned = strings.ReplaceAll(cleaned, "+1", "")
	
	// Check if it's exactly 10 digits
	if len(cleaned) != 10 {
		return errors.New("phone number must be 10 digits")
	}
	
	// Check if all characters are digits
	for _, char := range cleaned {
		if !unicode.IsDigit(char) {
			return errors.New("phone number can only contain digits")
		}
	}
	
	return nil
}

// ValidateURL checks if a URL is valid (basic validation)
func ValidateURL(url string) error {
	if url == "" {
		return errors.New("URL cannot be empty")
	}
	
	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		return errors.New("URL must start with http:// or https://")
	}
	
	if len(url) < 10 {
		return errors.New("URL is too short")
	}
	
	if len(url) > 2048 {
		return errors.New("URL is too long")
	}
	
	return nil
}

// ValidateCreditCard checks if a credit card number is valid (Luhn algorithm)
func ValidateCreditCard(cardNumber string) error {
	// Remove spaces and dashes
	cleaned := strings.ReplaceAll(cardNumber, " ", "")
	cleaned = strings.ReplaceAll(cleaned, "-", "")
	
	if len(cleaned) < 13 || len(cleaned) > 19 {
		return errors.New("credit card number must be between 13 and 19 digits")
	}
	
	// Check if all characters are digits
	for _, char := range cleaned {
		if !unicode.IsDigit(char) {
			return errors.New("credit card number can only contain digits")
		}
	}
	
	// Luhn algorithm
	sum := 0
	alternate := false
	
	// Process digits from right to left
	for i := len(cleaned) - 1; i >= 0; i-- {
		digit := int(cleaned[i] - '0')
		
		if alternate {
			digit *= 2
			if digit > 9 {
				digit = digit%10 + digit/10
			}
		}
		
		sum += digit
		alternate = !alternate
	}
	
	if sum%10 != 0 {
		return errors.New("invalid credit card number")
	}
	
	return nil
}

// User represents a user with validation
type User struct {
	Username string
	Email    string
	Age      int
	Phone    string
}

// ValidateUser validates all user fields
func ValidateUser(user User) []error {
	var errors []error
	
	if err := ValidateUsername(user.Username); err != nil {
		errors = append(errors, err)
	}
	
	if err := ValidateEmail(user.Email); err != nil {
		errors = append(errors, err)
	}
	
	if err := ValidateAge(user.Age); err != nil {
		errors = append(errors, err)
	}
	
	if user.Phone != "" { // Phone is optional
		if err := ValidatePhoneNumber(user.Phone); err != nil {
			errors = append(errors, err)
		}
	}
	
	return errors
}
