// Package declaration
package main

// Import fmt for input/output
import "fmt"

// STRUCT DEFINITIONS

// Rectangle struct
type Rectangle struct {
	Width  float64
	Height float64
}

// Circle struct
type Circle struct {
	Radius float64
}

// BankAccount struct
type BankAccount struct {
	AccountNumber string
	HolderName    string
	Balance       float64
}

// Person struct
type Person struct {
	FirstName string
	LastName  string
	Age       int
	Email     string
}

// METHODS ON RECTANGLE

// Method to calculate area
// (r Rectangle) is the receiver - it means this method belongs to Rectangle
func (r Rectangle) Area() float64 {
	return r.Width * r.Height
}

// Method to calculate perimeter
func (r Rectangle) Perimeter() float64 {
	return 2 * (r.Width + r.Height)
}

// Method to check if it's a square
func (r Rectangle) IsSquare() bool {
	return r.Width == r.Height
}

// Method to display rectangle info
func (r Rectangle) Display() {
	fmt.Printf("Rectangle: %.1f x %.1f\n", r.Width, r.Height)
	fmt.Printf("  Area: %.2f\n", r.Area())
	fmt.Printf("  Perimeter: %.2f\n", r.Perimeter())
	fmt.Printf("  Is Square: %t\n", r.IsSquare())
}

// METHODS ON CIRCLE

// Method to calculate area
func (c Circle) Area() float64 {
	const pi = 3.14159
	return pi * c.Radius * c.Radius
}

// Method to calculate circumference
func (c Circle) Circumference() float64 {
	const pi = 3.14159
	return 2 * pi * c.Radius
}

// Method to display circle info
func (c Circle) Display() {
	fmt.Printf("Circle: radius %.1f\n", c.Radius)
	fmt.Printf("  Area: %.2f\n", c.Area())
	fmt.Printf("  Circumference: %.2f\n", c.Circumference())
}

// METHODS ON BANKACCOUNT

// Method to deposit money (pointer receiver to modify the struct)
func (ba *BankAccount) Deposit(amount float64) {
	if amount > 0 {
		ba.Balance += amount
		fmt.Printf("Deposited $%.2f. New balance: $%.2f\n", amount, ba.Balance)
	} else {
		fmt.Println("Deposit amount must be positive")
	}
}

// Method to withdraw money (pointer receiver to modify the struct)
func (ba *BankAccount) Withdraw(amount float64) bool {
	if amount <= 0 {
		fmt.Println("Withdrawal amount must be positive")
		return false
	}
	
	if amount > ba.Balance {
		fmt.Printf("Insufficient funds. Balance: $%.2f, Requested: $%.2f\n", 
			ba.Balance, amount)
		return false
	}
	
	ba.Balance -= amount
	fmt.Printf("Withdrew $%.2f. New balance: $%.2f\n", amount, ba.Balance)
	return true
}

// Method to get balance (value receiver - doesn't modify)
func (ba BankAccount) GetBalance() float64 {
	return ba.Balance
}

// Method to display account info
func (ba BankAccount) DisplayAccount() {
	fmt.Printf("Account: %s\n", ba.AccountNumber)
	fmt.Printf("Holder: %s\n", ba.HolderName)
	fmt.Printf("Balance: $%.2f\n", ba.Balance)
}

// METHODS ON PERSON

// Method to get full name
func (p Person) FullName() string {
	return p.FirstName + " " + p.LastName
}

// Method to check if person is adult
func (p Person) IsAdult() bool {
	return p.Age >= 18
}

// Method to have a birthday (pointer receiver to modify age)
func (p *Person) HaveBirthday() {
	p.Age++
	fmt.Printf("Happy Birthday %s! You are now %d years old.\n", 
		p.FullName(), p.Age)
}

// Method to update email (pointer receiver to modify)
func (p *Person) UpdateEmail(newEmail string) {
	oldEmail := p.Email
	p.Email = newEmail
	fmt.Printf("Email updated from %s to %s\n", oldEmail, newEmail)
}

// Method to introduce person
func (p Person) Introduce() {
	fmt.Printf("Hi, I'm %s, %d years old. Contact me at %s\n", 
		p.FullName(), p.Age, p.Email)
}

// Main function
func main() {
	fmt.Println("=== METHODS ON STRUCTS ===")
	fmt.Println()
	
	// RECTANGLE METHODS
	fmt.Println("=== Rectangle Methods ===")
	
	rect1 := Rectangle{Width: 5.0, Height: 3.0}
	rect2 := Rectangle{Width: 4.0, Height: 4.0} // Square
	
	rect1.Display()
	fmt.Println()
	rect2.Display()
	fmt.Println()
	
	// CIRCLE METHODS
	fmt.Println("=== Circle Methods ===")
	
	circle1 := Circle{Radius: 2.5}
	circle2 := Circle{Radius: 10.0}
	
	circle1.Display()
	fmt.Println()
	circle2.Display()
	fmt.Println()
	
	// BANK ACCOUNT METHODS
	fmt.Println("=== Bank Account Methods ===")
	
	account := BankAccount{
		AccountNumber: "ACC-001",
		HolderName:    "Alice Johnson",
		Balance:       1000.00,
	}
	
	account.DisplayAccount()
	fmt.Println()
	
	// Test deposit and withdrawal
	account.Deposit(250.00)
	account.Withdraw(100.00)
	account.Withdraw(2000.00) // Should fail
	
	fmt.Printf("Current balance: $%.2f\n", account.GetBalance())
	fmt.Println()
	
	// PERSON METHODS
	fmt.Println("=== Person Methods ===")
	
	person := Person{
		FirstName: "Bob",
		LastName:  "Smith",
		Age:       17,
		Email:     "<EMAIL>",
	}
	
	person.Introduce()
	fmt.Printf("Is adult: %t\n", person.IsAdult())
	
	// Have a birthday
	person.HaveBirthday()
	fmt.Printf("Is adult now: %t\n", person.IsAdult())
	
	// Update email
	person.UpdateEmail("<EMAIL>")
	person.Introduce()
	
	fmt.Println()
	
	// COMPARING VALUE vs POINTER RECEIVERS
	fmt.Println("=== Value vs Pointer Receivers ===")
	
	// Create two identical rectangles
	rectA := Rectangle{Width: 3.0, Height: 4.0}
	rectB := Rectangle{Width: 3.0, Height: 4.0}
	
	fmt.Printf("RectA area: %.2f\n", rectA.Area()) // Value receiver
	fmt.Printf("RectB area: %.2f\n", rectB.Area()) // Value receiver
	
	// Create two bank accounts
	accountA := BankAccount{
		AccountNumber: "ACC-A",
		HolderName:    "Person A",
		Balance:       500.00,
	}
	
	accountB := BankAccount{
		AccountNumber: "ACC-B", 
		HolderName:    "Person B",
		Balance:       500.00,
	}
	
	fmt.Printf("AccountA balance: $%.2f\n", accountA.GetBalance())
	fmt.Printf("AccountB balance: $%.2f\n", accountB.GetBalance())
	
	// Deposit to both accounts
	accountA.Deposit(100.00) // Pointer receiver - modifies original
	accountB.Deposit(200.00) // Pointer receiver - modifies original
	
	fmt.Printf("After deposits:\n")
	fmt.Printf("AccountA balance: $%.2f\n", accountA.GetBalance())
	fmt.Printf("AccountB balance: $%.2f\n", accountB.GetBalance())
	
	fmt.Println()
	
	// METHOD CHAINING EXAMPLE
	fmt.Println("=== Method Usage Examples ===")
	
	// Calculate areas of different shapes
	shapes := []interface{}{
		Rectangle{Width: 5, Height: 3},
		Circle{Radius: 2},
		Rectangle{Width: 2, Height: 2},
		Circle{Radius: 1.5},
	}
	
	fmt.Println("Shape areas:")
	for i, shape := range shapes {
		switch s := shape.(type) {
		case Rectangle:
			fmt.Printf("Shape %d (Rectangle): Area = %.2f\n", i+1, s.Area())
		case Circle:
			fmt.Printf("Shape %d (Circle): Area = %.2f\n", i+1, s.Area())
		}
	}
	
	fmt.Println()
	
	// Multiple people having birthdays
	people := []*Person{
		{"Alice", "Johnson", 25, "<EMAIL>"},
		{"Charlie", "Brown", 30, "<EMAIL>"},
		{"Diana", "Prince", 28, "<EMAIL>"},
	}
	
	fmt.Println("Birthday party:")
	for _, person := range people {
		person.HaveBirthday()
	}
	
	fmt.Println()
	fmt.Println("Methods on structs demonstration complete! 🎯")
}
