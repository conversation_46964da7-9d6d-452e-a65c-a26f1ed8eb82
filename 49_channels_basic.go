// Package declaration
package main

// Import packages
import (
	"fmt"
	"time"
)

// BASIC CHANNEL OPERATIONS

// Function that sends data to a channel
func sendData(ch chan string, data string) {
	fmt.Printf("Sending: %s\n", data)
	ch <- data // Send data to channel
	fmt.Printf("Sent: %s\n", data)
}

// Function that receives data from a channel
func receiveData(ch chan string) {
	data := <-ch // Receive data from channel
	fmt.Printf("Received: %s\n", data)
}

// Function that sends numbers to a channel
func sendNumbers(ch chan int, start, end int) {
	fmt.Printf("Sending numbers from %d to %d\n", start, end)
	
	for i := start; i <= end; i++ {
		ch <- i // Send each number
		fmt.Printf("Sent: %d\n", i)
		time.Sleep(100 * time.Millisecond) // Small delay
	}
	
	close(ch) // Close channel when done sending
	fmt.Println("Channel closed by sender")
}

// Function that receives all numbers from a channel
func receiveNumbers(ch chan int) {
	fmt.Println("Starting to receive numbers...")
	
	// Receive until channel is closed
	for number := range ch {
		fmt.Printf("Received: %d\n", number)
		time.Sleep(50 * time.Millisecond) // Process time
	}
	
	fmt.Println("Finished receiving (channel was closed)")
}

// Function that demonstrates buffered channels
func demonstrateBufferedChannel() {
	fmt.Println("\n=== Buffered Channel Demo ===")
	
	// Create buffered channel with capacity of 3
	ch := make(chan string, 3)
	
	// Send data without blocking (up to buffer size)
	ch <- "Message 1"
	ch <- "Message 2"
	ch <- "Message 3"
	
	fmt.Println("Sent 3 messages to buffered channel")
	
	// Receive the messages
	for i := 0; i < 3; i++ {
		msg := <-ch
		fmt.Printf("Received from buffer: %s\n", msg)
	}
}

// Function that simulates work and sends result
func doWorkAndSendResult(workerID int, ch chan string) {
	workTime := time.Duration(100+workerID*50) * time.Millisecond
	
	fmt.Printf("Worker %d: Starting work (will take %v)\n", workerID, workTime)
	time.Sleep(workTime) // Simulate work
	
	result := fmt.Sprintf("Result from Worker %d", workerID)
	ch <- result
	
	fmt.Printf("Worker %d: Work completed, result sent\n", workerID)
}

// Main function
func main() {
	fmt.Println("=== CHANNELS IN GO ===")
	fmt.Println()
	
	// BASIC CHANNEL USAGE
	fmt.Println("=== Basic Channel Usage ===")
	
	// Create an unbuffered channel
	ch := make(chan string)
	
	// Start goroutine to send data
	go sendData(ch, "Hello, Channel!")
	
	// Receive data in main goroutine
	receiveData(ch)
	
	fmt.Println()
	
	// CHANNEL WITH MULTIPLE VALUES
	fmt.Println("=== Channel with Multiple Values ===")
	
	// Create channel for integers
	numberCh := make(chan int)
	
	// Start sender goroutine
	go sendNumbers(numberCh, 1, 5)
	
	// Start receiver goroutine
	go receiveNumbers(numberCh)
	
	// Wait for goroutines to complete
	time.Sleep(1 * time.Second)
	
	// BUFFERED CHANNELS
	demonstrateBufferedChannel()
	
	fmt.Println()
	
	// MULTIPLE WORKERS WITH CHANNELS
	fmt.Println("=== Multiple Workers with Channels ===")
	
	// Create channel for results
	resultCh := make(chan string, 3) // Buffered for 3 results
	
	// Start multiple workers
	for i := 1; i <= 3; i++ {
		go doWorkAndSendResult(i, resultCh)
	}
	
	// Collect results
	fmt.Println("Waiting for worker results...")
	for i := 0; i < 3; i++ {
		result := <-resultCh
		fmt.Printf("Main received: %s\n", result)
	}
	
	fmt.Println()
	
	// CHANNEL DIRECTIONS (send-only, receive-only)
	fmt.Println("=== Channel Directions ===")
	
	// Function that only sends to channel
	sender := func(ch chan<- string) { // chan<- means send-only
		ch <- "Message from sender-only channel"
	}
	
	// Function that only receives from channel
	receiver := func(ch <-chan string) { // <-chan means receive-only
		msg := <-ch
		fmt.Printf("Received from receive-only channel: %s\n", msg)
	}
	
	// Create bidirectional channel
	directionCh := make(chan string)
	
	go sender(directionCh)   // Pass as send-only
	go receiver(directionCh) // Pass as receive-only
	
	time.Sleep(100 * time.Millisecond)
	
	fmt.Println()
	
	// CHANNEL SYNCHRONIZATION
	fmt.Println("=== Channel Synchronization ===")
	
	// Channel used for synchronization (no data, just signal)
	done := make(chan bool)
	
	go func() {
		fmt.Println("Goroutine: Starting work...")
		time.Sleep(200 * time.Millisecond)
		fmt.Println("Goroutine: Work completed!")
		done <- true // Signal completion
	}()
	
	fmt.Println("Main: Waiting for goroutine to complete...")
	<-done // Wait for signal
	fmt.Println("Main: Goroutine completed, continuing...")
	
	fmt.Println()
	
	// PRODUCER-CONSUMER PATTERN
	fmt.Println("=== Producer-Consumer Pattern ===")
	
	// Create channel for data
	dataCh := make(chan int, 5) // Buffered channel
	
	// Producer goroutine
	go func() {
		fmt.Println("Producer: Starting production...")
		for i := 1; i <= 10; i++ {
			fmt.Printf("Producer: Producing item %d\n", i)
			dataCh <- i
			time.Sleep(50 * time.Millisecond)
		}
		close(dataCh) // Signal no more data
		fmt.Println("Producer: Production completed")
	}()
	
	// Consumer goroutine
	go func() {
		fmt.Println("Consumer: Starting consumption...")
		for item := range dataCh { // Receive until channel is closed
			fmt.Printf("Consumer: Consuming item %d\n", item)
			time.Sleep(80 * time.Millisecond) // Slower consumer
		}
		fmt.Println("Consumer: Consumption completed")
	}()
	
	// Wait for producer and consumer to finish
	time.Sleep(1200 * time.Millisecond)
	
	fmt.Println()
	
	// CHANNEL CLOSING AND CHECKING
	fmt.Println("=== Channel Closing and Checking ===")
	
	statusCh := make(chan string, 2)
	
	// Send some data
	statusCh <- "Status 1"
	statusCh <- "Status 2"
	close(statusCh) // Close the channel
	
	// Receive with ok pattern to check if channel is closed
	for {
		status, ok := <-statusCh
		if !ok {
			fmt.Println("Channel is closed, no more data")
			break
		}
		fmt.Printf("Received status: %s\n", status)
	}
	
	fmt.Println()
	
	// TIMEOUT WITH CHANNELS
	fmt.Println("=== Timeout with Channels ===")
	
	slowCh := make(chan string)
	
	// Slow goroutine
	go func() {
		time.Sleep(300 * time.Millisecond)
		slowCh <- "Finally done!"
	}()
	
	// Try to receive with timeout
	select {
	case result := <-slowCh:
		fmt.Printf("Received result: %s\n", result)
	case <-time.After(200 * time.Millisecond):
		fmt.Println("Timeout! Operation took too long")
	}
	
	// Wait for the slow goroutine to actually finish
	time.Sleep(200 * time.Millisecond)
	
	fmt.Println()
	
	// CHANNEL BEST PRACTICES
	fmt.Println("=== Channel Best Practices ===")
	fmt.Println("1. Close channels when no more data will be sent")
	fmt.Println("2. Only the sender should close the channel")
	fmt.Println("3. Use buffered channels to avoid blocking")
	fmt.Println("4. Use select for non-blocking operations")
	fmt.Println("5. Check if channel is closed when receiving")
	fmt.Println("6. Use channel directions to enforce usage patterns")
	
	fmt.Println()
	fmt.Println("Channels demonstration complete! 📡")
	fmt.Println("Next: Learn about select statement for advanced channel operations!")
}
