// Package declaration
package main

// Import packages
import (
	"errors"
	"fmt"
)

// ERROR WRAPPING EXAMPLES

// Simulate different layers of an application

// Database layer - lowest level
func connectToDatabase() error {
	// Simulate database connection failure
	return errors.New("connection refused")
}

func queryUser(userID int) (string, error) {
	// Try to connect to database
	if err := connectToDatabase(); err != nil {
		// Wrap the error with context
		return "", fmt.Errorf("failed to connect to database for user query: %w", err)
	}
	
	// Simulate user not found
	if userID == 999 {
		return "", errors.New("user not found")
	}
	
	return fmt.Sprintf("User-%d", userID), nil
}

// Service layer - middle level
func getUserProfile(userID int) (map[string]interface{}, error) {
	// Query user from database
	username, err := queryUser(userID)
	if err != nil {
		// Wrap with service layer context
		return nil, fmt.Errorf("getUserProfile failed for userID %d: %w", userID, err)
	}
	
	// Simulate profile building
	profile := map[string]interface{}{
		"id":   userID,
		"name": username,
	}
	
	return profile, nil
}

// API layer - highest level
func handleUserRequest(userID int) error {
	profile, err := getUserProfile(userID)
	if err != nil {
		// Wrap with API layer context
		return fmt.Errorf("API request failed for user %d: %w", userID, err)
	}
	
	fmt.Printf("✅ User profile retrieved: %v\n", profile)
	return nil
}

// File processing example
func readConfigFile(filename string) (string, error) {
	// Simulate file reading errors
	switch filename {
	case "config.json":
		return `{"setting": "value"}`, nil
	case "missing.json":
		return "", errors.New("no such file or directory")
	case "locked.json":
		return "", errors.New("permission denied")
	default:
		return "", errors.New("unknown file error")
	}
}

func parseConfig(filename string) (map[string]string, error) {
	content, err := readConfigFile(filename)
	if err != nil {
		// Wrap with parsing context
		return nil, fmt.Errorf("failed to read config file '%s': %w", filename, err)
	}
	
	// Simulate parsing
	if content == "" {
		return nil, errors.New("config file is empty")
	}
	
	// Simple parsing simulation
	config := map[string]string{
		"setting": "value",
	}
	
	return config, nil
}

func initializeApp(configFile string) error {
	config, err := parseConfig(configFile)
	if err != nil {
		// Wrap with application initialization context
		return fmt.Errorf("application initialization failed: %w", err)
	}
	
	fmt.Printf("✅ App initialized with config: %v\n", config)
	return nil
}

// Network request example
func makeHTTPRequest(url string) (string, error) {
	// Simulate HTTP errors
	switch url {
	case "https://api.example.com/data":
		return "response data", nil
	case "https://timeout.example.com":
		return "", errors.New("request timeout")
	case "https://notfound.example.com":
		return "", errors.New("404 not found")
	default:
		return "", errors.New("network error")
	}
}

func fetchUserData(userID int) (string, error) {
	url := fmt.Sprintf("https://api.example.com/users/%d", userID)
	
	data, err := makeHTTPRequest(url)
	if err != nil {
		// Wrap with fetch context
		return "", fmt.Errorf("failed to fetch user data for ID %d from %s: %w", userID, url, err)
	}
	
	return data, nil
}

func processUserData(userID int) error {
	data, err := fetchUserData(userID)
	if err != nil {
		// Wrap with processing context
		return fmt.Errorf("user data processing failed: %w", err)
	}
	
	fmt.Printf("✅ Processed user data: %s\n", data)
	return nil
}

// Error unwrapping functions
func demonstrateErrorUnwrapping() {
	fmt.Println("=== Error Unwrapping Demonstration ===")
	
	// Create a wrapped error
	originalErr := errors.New("original error")
	wrappedErr := fmt.Errorf("wrapped: %w", originalErr)
	doubleWrappedErr := fmt.Errorf("double wrapped: %w", wrappedErr)
	
	fmt.Printf("Original error: %v\n", originalErr)
	fmt.Printf("Wrapped error: %v\n", wrappedErr)
	fmt.Printf("Double wrapped error: %v\n", doubleWrappedErr)
	
	// Unwrap errors
	fmt.Println("\nUnwrapping:")
	
	// Unwrap once
	unwrapped1 := errors.Unwrap(doubleWrappedErr)
	fmt.Printf("After first unwrap: %v\n", unwrapped1)
	
	// Unwrap again
	unwrapped2 := errors.Unwrap(unwrapped1)
	fmt.Printf("After second unwrap: %v\n", unwrapped2)
	
	// Try to unwrap original (should return nil)
	unwrapped3 := errors.Unwrap(unwrapped2)
	fmt.Printf("After third unwrap: %v\n", unwrapped3)
	
	// Check if error is a specific error
	fmt.Println("\nError checking:")
	fmt.Printf("Is double wrapped error the original? %t\n", errors.Is(doubleWrappedErr, originalErr))
	fmt.Printf("Is wrapped error the original? %t\n", errors.Is(wrappedErr, originalErr))
	fmt.Printf("Is original error itself? %t\n", errors.Is(originalErr, originalErr))
}

// Main function
func main() {
	fmt.Println("=== ERROR WRAPPING IN GO ===")
	fmt.Println()
	
	// LAYERED APPLICATION ERRORS
	fmt.Println("=== Layered Application Errors ===")
	
	userIDs := []int{123, 999, 456}
	
	for _, userID := range userIDs {
		fmt.Printf("Handling request for user ID: %d\n", userID)
		
		if err := handleUserRequest(userID); err != nil {
			fmt.Printf("❌ Request failed: %v\n", err)
			
			// Show the error chain
			fmt.Println("Error chain:")
			currentErr := err
			level := 1
			
			for currentErr != nil {
				fmt.Printf("  Level %d: %v\n", level, currentErr)
				currentErr = errors.Unwrap(currentErr)
				level++
			}
		}
		fmt.Println()
	}
	
	// FILE PROCESSING ERRORS
	fmt.Println("=== File Processing Errors ===")
	
	configFiles := []string{"config.json", "missing.json", "locked.json", "unknown.json"}
	
	for _, filename := range configFiles {
		fmt.Printf("Initializing app with config: %s\n", filename)
		
		if err := initializeApp(filename); err != nil {
			fmt.Printf("❌ Initialization failed: %v\n", err)
			
			// Check for specific underlying errors
			if errors.Is(err, errors.New("no such file or directory")) {
				fmt.Println("   → This is a file not found error")
			} else if errors.Is(err, errors.New("permission denied")) {
				fmt.Println("   → This is a permission error")
			}
		}
		fmt.Println()
	}
	
	// NETWORK REQUEST ERRORS
	fmt.Println("=== Network Request Errors ===")
	
	userIDs = []int{1, 2, 3}
	
	for _, userID := range userIDs {
		fmt.Printf("Processing user data for ID: %d\n", userID)
		
		if err := processUserData(userID); err != nil {
			fmt.Printf("❌ Processing failed: %v\n", err)
			
			// Trace the error back to its source
			fmt.Println("Tracing error source:")
			currentErr := err
			depth := 0
			
			for currentErr != nil {
				indent := ""
				for i := 0; i < depth; i++ {
					indent += "  "
				}
				fmt.Printf("%s→ %v\n", indent, currentErr)
				currentErr = errors.Unwrap(currentErr)
				depth++
			}
		}
		fmt.Println()
	}
	
	// ERROR UNWRAPPING DEMONSTRATION
	demonstrateErrorUnwrapping()
	
	fmt.Println()
	
	// PRACTICAL ERROR HANDLING PATTERNS
	fmt.Println("=== Practical Error Handling Patterns ===")
	
	// Pattern 1: Check for specific errors in wrapped chain
	err := handleUserRequest(999) // This will fail
	if err != nil {
		if errors.Is(err, errors.New("user not found")) {
			fmt.Println("✅ Detected 'user not found' error in chain")
		}
		
		// You can also check for error strings (less reliable)
		if fmt.Sprintf("%v", err) != "" {
			fmt.Println("✅ Error has content")
		}
	}
	
	// Pattern 2: Extract context from wrapped errors
	fmt.Println("\nExtracting context from wrapped errors:")
	complexErr := fmt.Errorf("operation failed at %s: %w", 
		"2024-01-01 12:00:00", 
		fmt.Errorf("validation error: %w", 
			errors.New("field 'email' is required")))
	
	fmt.Printf("Full error: %v\n", complexErr)
	
	// Walk through the error chain
	currentErr := complexErr
	for currentErr != nil {
		fmt.Printf("  Current: %v\n", currentErr)
		currentErr = errors.Unwrap(currentErr)
	}
	
	fmt.Println()
	
	// ERROR WRAPPING BEST PRACTICES
	fmt.Println("=== Error Wrapping Best Practices ===")
	fmt.Println("1. Use %w verb in fmt.Errorf to wrap errors")
	fmt.Println("2. Add meaningful context at each layer")
	fmt.Println("3. Don't wrap errors unnecessarily")
	fmt.Println("4. Use errors.Is() to check for specific errors")
	fmt.Println("5. Use errors.Unwrap() to access underlying errors")
	fmt.Println("6. Preserve the original error information")
	fmt.Println("7. Add context that helps with debugging")
	
	// GOOD VS BAD WRAPPING EXAMPLES
	fmt.Println("\n=== Good vs Bad Wrapping Examples ===")
	
	originalError := errors.New("database connection failed")
	
	// BAD: No context
	badWrap := fmt.Errorf("error: %w", originalError)
	fmt.Printf("❌ Bad wrapping: %v\n", badWrap)
	
	// GOOD: Meaningful context
	goodWrap := fmt.Errorf("failed to save user profile to database: %w", originalError)
	fmt.Printf("✅ Good wrapping: %v\n", goodWrap)
	
	// BAD: Losing original error
	lostError := fmt.Errorf("something went wrong: %v", originalError) // Note: %v not %w
	fmt.Printf("❌ Lost original (can't unwrap): %v\n", lostError)
	fmt.Printf("   Unwrap result: %v\n", errors.Unwrap(lostError))
	
	// GOOD: Preserving original error
	preservedError := fmt.Errorf("user service error: %w", originalError)
	fmt.Printf("✅ Preserved original (can unwrap): %v\n", preservedError)
	fmt.Printf("   Unwrap result: %v\n", errors.Unwrap(preservedError))
	
	fmt.Println()
	fmt.Println("Error wrapping demonstration complete! 🔗")
	fmt.Println("Key takeaway: Error wrapping creates a trail of context for better debugging!")
}
