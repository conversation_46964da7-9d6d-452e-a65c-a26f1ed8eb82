// Package declaration
package main

// Import packages
import (
	"bufio"
	"fmt"
	"os"
	"time"
)

// WRITING FILES EXAMPLES

// Function to write entire content at once
func writeEntireFile(filename string, content string) error {
	fmt.Printf("=== Writing entire file: %s ===\n", filename)

	// Method 1: Using os.WriteFile (recommended for simple writes)
	// 0644 means: owner can read/write, group can read, others can read
	err := os.WriteFile(filename, []byte(content), 0644)
	if err != nil {
		return fmt.Errorf("failed to write file %s: %w", filename, err)
	}

	fmt.Printf("✅ Successfully wrote %d bytes to %s\n", len(content), filename)
	return nil
}

// Function to write file using os.Create
func writeFileWithCreate(filename string, lines []string) error {
	fmt.Printf("=== Writing file with os.Create: %s ===\n", filename)

	// Create the file (truncates if exists)
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create file %s: %w", filename, err)
	}
	defer file.Close() // Always close the file

	// Write each line
	for i, line := range lines {
		_, err := file.WriteString(line + "\n")
		if err != nil {
			return fmt.Errorf("failed to write line %d to %s: %w", i+1, filename, err)
		}
	}

	fmt.Printf("✅ Successfully wrote %d lines to %s\n", len(lines), filename)
	return nil
}

// Function to append to file
func appendToFile(filename string, content string) error {
	fmt.Printf("=== Appending to file: %s ===\n", filename)

	// Open file in append mode (create if doesn't exist)
	file, err := os.OpenFile(filename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("failed to open file %s for appending: %w", filename, err)
	}
	defer file.Close()

	// Append content
	_, err = file.WriteString(content)
	if err != nil {
		return fmt.Errorf("failed to append to file %s: %w", filename, err)
	}

	fmt.Printf("✅ Successfully appended %d bytes to %s\n", len(content), filename)
	return nil
}

// Function to write using buffered writer
func writeWithBuffer(filename string, data []string) error {
	fmt.Printf("=== Writing with buffer: %s ===\n", filename)

	// Create the file
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create file %s: %w", filename, err)
	}
	defer file.Close()

	// Create buffered writer
	writer := bufio.NewWriter(file)
	defer writer.Flush() // Ensure all data is written

	// Write data
	for i, item := range data {
		_, err := writer.WriteString(fmt.Sprintf("%d: %s\n", i+1, item))
		if err != nil {
			return fmt.Errorf("failed to write item %d to %s: %w", i+1, filename, err)
		}
	}

	fmt.Printf("✅ Successfully wrote %d items to %s using buffer\n", len(data), filename)
	return nil
}

// Function to write CSV file
func writeCSVFile(filename string, data [][]string) error {
	fmt.Printf("=== Writing CSV file: %s ===\n", filename)

	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create CSV file %s: %w", filename, err)
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	defer writer.Flush()

	// Write each row
	for rowIndex, row := range data {
		for colIndex, field := range row {
			if colIndex > 0 {
				writer.WriteString(",") // Add comma separator
			}
			writer.WriteString(field)
		}
		writer.WriteString("\n") // End of row

		fmt.Printf("Wrote row %d: %v\n", rowIndex+1, row)
	}

	fmt.Printf("✅ Successfully wrote %d rows to CSV file %s\n", len(data), filename)
	return nil
}

// Function to write log file with timestamps
func writeLogFile(filename string, logEntries []string) error {
	fmt.Printf("=== Writing log file: %s ===\n", filename)

	file, err := os.OpenFile(filename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return fmt.Errorf("failed to open log file %s: %w", filename, err)
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	defer writer.Flush()

	// Write each log entry with timestamp
	for _, entry := range logEntries {
		timestamp := time.Now().Format("2006-01-02 15:04:05")
		logLine := fmt.Sprintf("[%s] %s\n", timestamp, entry)

		_, err := writer.WriteString(logLine)
		if err != nil {
			return fmt.Errorf("failed to write log entry to %s: %w", filename, err)
		}

		fmt.Printf("Logged: %s", logLine)
	}

	fmt.Printf("✅ Successfully wrote %d log entries to %s\n", len(logEntries), filename)
	return nil
}

// Function to write configuration file
func writeConfigFile(filename string, config map[string]string) error {
	fmt.Printf("=== Writing config file: %s ===\n", filename)

	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("failed to create config file %s: %w", filename, err)
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	defer writer.Flush()

	// Write header comment
	writer.WriteString("# Configuration file\n")
	writer.WriteString("# Generated on " + time.Now().Format("2006-01-02 15:04:05") + "\n\n")

	// Write each configuration key-value pair
	for key, value := range config {
		configLine := fmt.Sprintf("%s=%s\n", key, value)
		writer.WriteString(configLine)
		fmt.Printf("Config: %s = %s\n", key, value)
	}

	fmt.Printf("✅ Successfully wrote %d config entries to %s\n", len(config), filename)
	return nil
}

// Function to demonstrate file permissions
func writeWithPermissions(filename string, content string, perm os.FileMode) error {
	fmt.Printf("=== Writing file with permissions %o: %s ===\n", perm, filename)

	err := os.WriteFile(filename, []byte(content), perm)
	if err != nil {
		return fmt.Errorf("failed to write file %s with permissions %o: %w", filename, perm, err)
	}

	// Check the actual permissions
	info, err := os.Stat(filename)
	if err != nil {
		return fmt.Errorf("failed to get file info for %s: %w", filename, err)
	}

	fmt.Printf("✅ File written with permissions: %o\n", info.Mode().Perm())
	return nil
}

// Main function
func main() {
	fmt.Println("=== FILE WRITING IN GO ===")
	fmt.Println()

	// WRITE ENTIRE FILE AT ONCE
	content := `Hello, World!
This is a sample file created by Go.
It demonstrates basic file writing.
Each line contains different content.
End of file.`

	if err := writeEntireFile("output.txt", content); err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	}
	fmt.Println()

	// WRITE FILE USING os.Create
	lines := []string{
		"Line 1: Introduction",
		"Line 2: Main content",
		"Line 3: Additional information",
		"Line 4: Conclusion",
	}

	if err := writeFileWithCreate("lines.txt", lines); err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	}
	fmt.Println()

	// APPEND TO FILE
	appendContent := "\nThis line was appended later.\n"
	if err := appendToFile("output.txt", appendContent); err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	}

	// Append more content
	moreContent := "Another appended line.\n"
	if err := appendToFile("output.txt", moreContent); err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	}
	fmt.Println()

	// WRITE WITH BUFFER
	data := []string{
		"Apple",
		"Banana",
		"Cherry",
		"Date",
		"Elderberry",
	}

	if err := writeWithBuffer("fruits.txt", data); err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	}
	fmt.Println()

	// WRITE CSV FILE
	csvData := [][]string{
		{"Name", "Age", "City"},
		{"Alice", "25", "New York"},
		{"Bob", "30", "Los Angeles"},
		{"Charlie", "35", "Chicago"},
		{"Diana", "28", "Seattle"},
	}

	if err := writeCSVFile("people.csv", csvData); err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	}
	fmt.Println()

	// WRITE LOG FILE
	logEntries := []string{
		"Application started",
		"User logged in: <EMAIL>",
		"Database connection established",
		"Processing user request",
		"Request completed successfully",
	}

	if err := writeLogFile("app.log", logEntries); err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	}
	fmt.Println()

	// WRITE CONFIGURATION FILE
	config := map[string]string{
		"server_port":     "8080",
		"database_host":   "localhost",
		"database_port":   "5432",
		"debug_mode":      "false",
		"max_connections": "100",
	}

	if err := writeConfigFile("app_config.txt", config); err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	}
	fmt.Println()

	// WRITE WITH DIFFERENT PERMISSIONS
	permissionTests := []struct {
		filename string
		perm     os.FileMode
		desc     string
	}{
		{"readonly.txt", 0444, "read-only"},
		{"readwrite.txt", 0644, "read-write for owner, read for others"},
		{"executable.txt", 0755, "executable"},
	}

	for _, test := range permissionTests {
		content := fmt.Sprintf("This file has %s permissions.\n", test.desc)
		if err := writeWithPermissions(test.filename, content, test.perm); err != nil {
			fmt.Printf("❌ Error: %v\n", err)
		}
	}
	fmt.Println()

	// ERROR HANDLING DEMONSTRATION
	fmt.Println("=== Error Handling Demonstration ===")

	// Try to write to invalid path
	if err := writeEntireFile("/invalid/path/file.txt", "content"); err != nil {
		fmt.Printf("❌ Expected error: %v\n", err)
	}

	// Try to write to directory
	if err := writeEntireFile(".", "content"); err != nil {
		fmt.Printf("❌ Expected error: %v\n", err)
	}
	fmt.Println()

	// VERIFY WRITTEN FILES
	fmt.Println("=== Verifying Written Files ===")

	filesToCheck := []string{
		"output.txt", "lines.txt", "fruits.txt", "people.csv",
		"app.log", "app_config.txt", "readonly.txt", "readwrite.txt", "executable.txt",
	}

	for _, filename := range filesToCheck {
		if info, err := os.Stat(filename); err == nil {
			fmt.Printf("✅ %s: %d bytes, permissions: %o\n",
				filename, info.Size(), info.Mode().Perm())
		} else {
			fmt.Printf("❌ %s: not found\n", filename)
		}
	}
	fmt.Println()

	// BEST PRACTICES
	fmt.Println("=== File Writing Best Practices ===")
	fmt.Println("1. Always check for errors when writing files")
	fmt.Println("2. Use defer to ensure files are closed")
	fmt.Println("3. Use appropriate file permissions (0644 for regular files)")
	fmt.Println("4. Use buffered writers for multiple small writes")
	fmt.Println("5. Flush buffers before closing files")
	fmt.Println("6. Use os.WriteFile for simple, one-time writes")
	fmt.Println("7. Use append mode for log files")
	fmt.Println("8. Consider atomic writes for critical data")

	// Clean up files (optional)
	fmt.Println("\nNote: Sample files created for demonstration.")
	fmt.Println("You can examine them and delete when no longer needed.")

	fmt.Println()
	fmt.Println("File writing demonstration complete! ✍️")
	fmt.Println("Next: Learn about file operations!")
}
