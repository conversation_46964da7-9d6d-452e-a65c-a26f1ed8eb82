// Web API Server - A complete REST API example demonstrating HTTP handling in Go
package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
)

// DATA MODELS

// User represents a user in our system
type User struct {
	ID        int       `json:"id"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CreateUserRequest represents the request body for creating a user
type CreateUserRequest struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

// UpdateUserRequest represents the request body for updating a user
type UpdateUserRequest struct {
	Name  string `json:"name,omitempty"`
	Email string `json:"email,omitempty"`
}

// APIResponse represents a standard API response
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// IN-MEMORY DATA STORE

// UserStore manages users in memory (in production, use a database)
type UserStore struct {
	users  map[int]*User
	nextID int
	mutex  sync.RWMutex
}

// NewUserStore creates a new user store
func NewUserStore() *UserStore {
	return &UserStore{
		users:  make(map[int]*User),
		nextID: 1,
		mutex:  sync.RWMutex{},
	}
}

// CreateUser adds a new user
func (us *UserStore) CreateUser(name, email string) *User {
	us.mutex.Lock()
	defer us.mutex.Unlock()
	
	user := &User{
		ID:        us.nextID,
		Name:      name,
		Email:     email,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	
	us.users[us.nextID] = user
	us.nextID++
	
	return user
}

// GetUser retrieves a user by ID
func (us *UserStore) GetUser(id int) (*User, bool) {
	us.mutex.RLock()
	defer us.mutex.RUnlock()
	
	user, exists := us.users[id]
	return user, exists
}

// GetAllUsers retrieves all users
func (us *UserStore) GetAllUsers() []*User {
	us.mutex.RLock()
	defer us.mutex.RUnlock()
	
	users := make([]*User, 0, len(us.users))
	for _, user := range us.users {
		users = append(users, user)
	}
	
	return users
}

// UpdateUser updates an existing user
func (us *UserStore) UpdateUser(id int, name, email string) (*User, bool) {
	us.mutex.Lock()
	defer us.mutex.Unlock()
	
	user, exists := us.users[id]
	if !exists {
		return nil, false
	}
	
	if name != "" {
		user.Name = name
	}
	if email != "" {
		user.Email = email
	}
	user.UpdatedAt = time.Now()
	
	return user, true
}

// DeleteUser removes a user
func (us *UserStore) DeleteUser(id int) bool {
	us.mutex.Lock()
	defer us.mutex.Unlock()
	
	_, exists := us.users[id]
	if exists {
		delete(us.users, id)
	}
	
	return exists
}

// API SERVER

// APIServer handles HTTP requests
type APIServer struct {
	userStore *UserStore
	port      string
}

// NewAPIServer creates a new API server
func NewAPIServer(port string) *APIServer {
	return &APIServer{
		userStore: NewUserStore(),
		port:      port,
	}
}

// MIDDLEWARE

// LoggingMiddleware logs HTTP requests
func (s *APIServer) LoggingMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		
		// Call the next handler
		next(w, r)
		
		// Log the request
		duration := time.Since(start)
		log.Printf("%s %s - %v", r.Method, r.URL.Path, duration)
	}
}

// CORSMiddleware adds CORS headers
func (s *APIServer) CORSMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
		
		// Handle preflight requests
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}
		
		next(w, r)
	}
}

// UTILITY FUNCTIONS

// WriteJSONResponse writes a JSON response
func WriteJSONResponse(w http.ResponseWriter, statusCode int, response APIResponse) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// WriteErrorResponse writes an error response
func WriteErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	response := APIResponse{
		Success: false,
		Error:   message,
	}
	WriteJSONResponse(w, statusCode, response)
}

// WriteSuccessResponse writes a success response
func WriteSuccessResponse(w http.ResponseWriter, data interface{}, message string) {
	response := APIResponse{
		Success: true,
		Message: message,
		Data:    data,
	}
	WriteJSONResponse(w, http.StatusOK, response)
}

// ValidateEmail performs basic email validation
func ValidateEmail(email string) bool {
	return strings.Contains(email, "@") && len(email) > 5
}

// API HANDLERS

// HealthHandler handles health check requests
func (s *APIServer) HealthHandler(w http.ResponseWriter, r *http.Request) {
	response := APIResponse{
		Success: true,
		Message: "Server is healthy",
		Data: map[string]interface{}{
			"timestamp": time.Now(),
			"status":    "ok",
		},
	}
	WriteJSONResponse(w, http.StatusOK, response)
}

// GetUsersHandler handles GET /users
func (s *APIServer) GetUsersHandler(w http.ResponseWriter, r *http.Request) {
	users := s.userStore.GetAllUsers()
	WriteSuccessResponse(w, users, "Users retrieved successfully")
}

// GetUserHandler handles GET /users/{id}
func (s *APIServer) GetUserHandler(w http.ResponseWriter, r *http.Request) {
	// Extract ID from URL path
	pathParts := strings.Split(r.URL.Path, "/")
	if len(pathParts) < 3 {
		WriteErrorResponse(w, http.StatusBadRequest, "User ID is required")
		return
	}
	
	id, err := strconv.Atoi(pathParts[2])
	if err != nil {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid user ID")
		return
	}
	
	user, exists := s.userStore.GetUser(id)
	if !exists {
		WriteErrorResponse(w, http.StatusNotFound, "User not found")
		return
	}
	
	WriteSuccessResponse(w, user, "User retrieved successfully")
}

// CreateUserHandler handles POST /users
func (s *APIServer) CreateUserHandler(w http.ResponseWriter, r *http.Request) {
	var req CreateUserRequest
	
	// Parse JSON request body
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid JSON")
		return
	}
	
	// Validate input
	if req.Name == "" {
		WriteErrorResponse(w, http.StatusBadRequest, "Name is required")
		return
	}
	
	if req.Email == "" {
		WriteErrorResponse(w, http.StatusBadRequest, "Email is required")
		return
	}
	
	if !ValidateEmail(req.Email) {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid email format")
		return
	}
	
	// Create user
	user := s.userStore.CreateUser(req.Name, req.Email)
	
	response := APIResponse{
		Success: true,
		Message: "User created successfully",
		Data:    user,
	}
	WriteJSONResponse(w, http.StatusCreated, response)
}

// UpdateUserHandler handles PUT /users/{id}
func (s *APIServer) UpdateUserHandler(w http.ResponseWriter, r *http.Request) {
	// Extract ID from URL path
	pathParts := strings.Split(r.URL.Path, "/")
	if len(pathParts) < 3 {
		WriteErrorResponse(w, http.StatusBadRequest, "User ID is required")
		return
	}
	
	id, err := strconv.Atoi(pathParts[2])
	if err != nil {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid user ID")
		return
	}
	
	var req UpdateUserRequest
	
	// Parse JSON request body
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid JSON")
		return
	}
	
	// Validate email if provided
	if req.Email != "" && !ValidateEmail(req.Email) {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid email format")
		return
	}
	
	// Update user
	user, exists := s.userStore.UpdateUser(id, req.Name, req.Email)
	if !exists {
		WriteErrorResponse(w, http.StatusNotFound, "User not found")
		return
	}
	
	WriteSuccessResponse(w, user, "User updated successfully")
}

// DeleteUserHandler handles DELETE /users/{id}
func (s *APIServer) DeleteUserHandler(w http.ResponseWriter, r *http.Request) {
	// Extract ID from URL path
	pathParts := strings.Split(r.URL.Path, "/")
	if len(pathParts) < 3 {
		WriteErrorResponse(w, http.StatusBadRequest, "User ID is required")
		return
	}
	
	id, err := strconv.Atoi(pathParts[2])
	if err != nil {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid user ID")
		return
	}
	
	// Delete user
	exists := s.userStore.DeleteUser(id)
	if !exists {
		WriteErrorResponse(w, http.StatusNotFound, "User not found")
		return
	}
	
	WriteSuccessResponse(w, nil, "User deleted successfully")
}

// ROUTING

// SetupRoutes configures HTTP routes
func (s *APIServer) SetupRoutes() {
	// Health check
	http.HandleFunc("/health", s.CORSMiddleware(s.LoggingMiddleware(s.HealthHandler)))
	
	// User routes
	http.HandleFunc("/users", s.CORSMiddleware(s.LoggingMiddleware(s.UsersRouter)))
}

// UsersRouter routes user-related requests
func (s *APIServer) UsersRouter(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case "GET":
		if strings.HasPrefix(r.URL.Path, "/users/") && len(strings.Split(r.URL.Path, "/")) == 3 {
			s.GetUserHandler(w, r)
		} else {
			s.GetUsersHandler(w, r)
		}
	case "POST":
		s.CreateUserHandler(w, r)
	case "PUT":
		s.UpdateUserHandler(w, r)
	case "DELETE":
		s.DeleteUserHandler(w, r)
	default:
		WriteErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
	}
}

// Start starts the HTTP server
func (s *APIServer) Start() {
	s.SetupRoutes()
	
	log.Printf("Starting server on port %s", s.port)
	log.Printf("Health check: http://localhost%s/health", s.port)
	log.Printf("API endpoints: http://localhost%s/users", s.port)
	
	if err := http.ListenAndServe(s.port, nil); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}

// Main function
func main() {
	// Create and start server
	server := NewAPIServer(":8080")
	
	// Add some sample data
	server.userStore.CreateUser("Alice Johnson", "<EMAIL>")
	server.userStore.CreateUser("Bob Smith", "<EMAIL>")
	server.userStore.CreateUser("Charlie Brown", "<EMAIL>")
	
	fmt.Println("=== Web API Server ===")
	fmt.Println("Starting REST API server...")
	fmt.Println()
	fmt.Println("Available endpoints:")
	fmt.Println("GET    /health           - Health check")
	fmt.Println("GET    /users            - Get all users")
	fmt.Println("GET    /users/{id}       - Get user by ID")
	fmt.Println("POST   /users            - Create new user")
	fmt.Println("PUT    /users/{id}       - Update user")
	fmt.Println("DELETE /users/{id}       - Delete user")
	fmt.Println()
	fmt.Println("Example requests:")
	fmt.Println("curl http://localhost:8080/health")
	fmt.Println("curl http://localhost:8080/users")
	fmt.Println("curl -X POST -H 'Content-Type: application/json' -d '{\"name\":\"John Doe\",\"email\":\"<EMAIL>\"}' http://localhost:8080/users")
	fmt.Println()
	
	server.Start()
}
