// Package declaration
package main

// Import packages
import (
	"fmt"
	"time"
)

// CUSTOM ERROR TYPES

// ValidationError represents a validation error with field information
type ValidationError struct {
	Field   string
	Value   interface{}
	Message string
}

// Implement the error interface
func (ve ValidationError) Error() string {
	return fmt.Sprintf("validation error in field '%s': %s (value: %v)", 
		ve.Field, ve.Message, ve.Value)
}

// NetworkError represents a network-related error
type NetworkError struct {
	URL        string
	StatusCode int
	Timestamp  time.Time
	Retryable  bool
}

func (ne NetworkError) Error() string {
	return fmt.Sprintf("network error: %s returned status %d at %v", 
		ne.URL, ne.StatusCode, ne.Timestamp.Format("15:04:05"))
}

// IsRetryable returns whether this error can be retried
func (ne NetworkError) IsRetryable() bool {
	return ne.Retryable
}

// DatabaseError represents a database operation error
type DatabaseError struct {
	Operation string
	Table     string
	Query     string
	Cause     error
}

func (de DatabaseError) Error() string {
	return fmt.Sprintf("database error during %s on table '%s': %v", 
		de.Operation, de.Table, de.Cause)
}

// Unwrap returns the underlying error (for error wrapping)
func (de DatabaseError) Unwrap() error {
	return de.Cause
}

// BusinessLogicError represents application-specific errors
type BusinessLogicError struct {
	Code    string
	Message string
	Details map[string]interface{}
}

func (ble BusinessLogicError) Error() string {
	return fmt.Sprintf("business logic error [%s]: %s", ble.Code, ble.Message)
}

// GetCode returns the error code
func (ble BusinessLogicError) GetCode() string {
	return ble.Code
}

// GetDetails returns additional error details
func (ble BusinessLogicError) GetDetails() map[string]interface{} {
	return ble.Details
}

// FUNCTIONS USING CUSTOM ERRORS

// Function that validates user input
func validateUser(name string, email string, age int) error {
	if name == "" {
		return ValidationError{
			Field:   "name",
			Value:   name,
			Message: "cannot be empty",
		}
	}
	
	if len(name) < 2 {
		return ValidationError{
			Field:   "name",
			Value:   name,
			Message: "must be at least 2 characters",
		}
	}
	
	if email == "" {
		return ValidationError{
			Field:   "email",
			Value:   email,
			Message: "cannot be empty",
		}
	}
	
	// Simple email validation
	if !contains(email, "@") {
		return ValidationError{
			Field:   "email",
			Value:   email,
			Message: "must contain @ symbol",
		}
	}
	
	if age < 0 {
		return ValidationError{
			Field:   "age",
			Value:   age,
			Message: "cannot be negative",
		}
	}
	
	if age > 150 {
		return ValidationError{
			Field:   "age",
			Value:   age,
			Message: "seems unrealistic",
		}
	}
	
	return nil
}

// Helper function to check if string contains substring
func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// Function that simulates network requests
func makeRequest(url string) error {
	// Simulate different network conditions
	switch url {
	case "https://api.example.com/users":
		return nil // Success
	case "https://api.example.com/timeout":
		return NetworkError{
			URL:        url,
			StatusCode: 408,
			Timestamp:  time.Now(),
			Retryable:  true,
		}
	case "https://api.example.com/notfound":
		return NetworkError{
			URL:        url,
			StatusCode: 404,
			Timestamp:  time.Now(),
			Retryable:  false,
		}
	case "https://api.example.com/server-error":
		return NetworkError{
			URL:        url,
			StatusCode: 500,
			Timestamp:  time.Now(),
			Retryable:  true,
		}
	default:
		return NetworkError{
			URL:        url,
			StatusCode: 400,
			Timestamp:  time.Now(),
			Retryable:  false,
		}
	}
}

// Function that simulates database operations
func queryDatabase(operation, table, query string) error {
	// Simulate different database errors
	switch table {
	case "users":
		return nil // Success
	case "nonexistent":
		return DatabaseError{
			Operation: operation,
			Table:     table,
			Query:     query,
			Cause:     fmt.Errorf("table does not exist"),
		}
	case "locked":
		return DatabaseError{
			Operation: operation,
			Table:     table,
			Query:     query,
			Cause:     fmt.Errorf("table is locked"),
		}
	default:
		return DatabaseError{
			Operation: operation,
			Table:     table,
			Query:     query,
			Cause:     fmt.Errorf("unknown database error"),
		}
	}
}

// Function that implements business logic
func processOrder(orderID string, amount float64) error {
	if orderID == "" {
		return BusinessLogicError{
			Code:    "INVALID_ORDER_ID",
			Message: "Order ID cannot be empty",
			Details: map[string]interface{}{
				"orderID": orderID,
			},
		}
	}
	
	if amount <= 0 {
		return BusinessLogicError{
			Code:    "INVALID_AMOUNT",
			Message: "Order amount must be positive",
			Details: map[string]interface{}{
				"orderID": orderID,
				"amount":  amount,
			},
		}
	}
	
	if amount > 10000 {
		return BusinessLogicError{
			Code:    "AMOUNT_TOO_HIGH",
			Message: "Order amount exceeds maximum limit",
			Details: map[string]interface{}{
				"orderID":  orderID,
				"amount":   amount,
				"maxLimit": 10000,
			},
		}
	}
	
	return nil
}

// Main function
func main() {
	fmt.Println("=== CUSTOM ERRORS IN GO ===")
	fmt.Println()
	
	// VALIDATION ERRORS
	fmt.Println("=== Validation Errors ===")
	
	users := []struct {
		name  string
		email string
		age   int
	}{
		{"Alice", "<EMAIL>", 25},
		{"", "<EMAIL>", 30},        // Empty name
		{"A", "<EMAIL>", 35},   // Name too short
		{"Diana", "", 28},                  // Empty email
		{"Eve", "invalid-email", 32},       // Invalid email
		{"Frank", "<EMAIL>", -5}, // Negative age
		{"Grace", "<EMAIL>", 200}, // Unrealistic age
	}
	
	for _, user := range users {
		fmt.Printf("Validating user: %s, %s, %d\n", user.name, user.email, user.age)
		
		if err := validateUser(user.name, user.email, user.age); err != nil {
			// Check if it's a ValidationError
			if validationErr, ok := err.(ValidationError); ok {
				fmt.Printf("❌ Validation Error - Field: %s, Value: %v, Message: %s\n",
					validationErr.Field, validationErr.Value, validationErr.Message)
			} else {
				fmt.Printf("❌ Unknown error: %v\n", err)
			}
		} else {
			fmt.Println("✅ User validation passed")
		}
		fmt.Println()
	}
	
	// NETWORK ERRORS
	fmt.Println("=== Network Errors ===")
	
	urls := []string{
		"https://api.example.com/users",
		"https://api.example.com/timeout",
		"https://api.example.com/notfound",
		"https://api.example.com/server-error",
		"https://invalid.url",
	}
	
	for _, url := range urls {
		fmt.Printf("Making request to: %s\n", url)
		
		if err := makeRequest(url); err != nil {
			// Check if it's a NetworkError
			if networkErr, ok := err.(NetworkError); ok {
				fmt.Printf("❌ Network Error - Status: %d, Time: %v\n",
					networkErr.StatusCode, networkErr.Timestamp.Format("15:04:05"))
				
				if networkErr.IsRetryable() {
					fmt.Println("🔄 This error is retryable")
				} else {
					fmt.Println("🚫 This error is not retryable")
				}
			} else {
				fmt.Printf("❌ Unknown error: %v\n", err)
			}
		} else {
			fmt.Println("✅ Request successful")
		}
		fmt.Println()
	}
	
	// DATABASE ERRORS
	fmt.Println("=== Database Errors ===")
	
	queries := []struct {
		operation string
		table     string
		query     string
	}{
		{"SELECT", "users", "SELECT * FROM users"},
		{"INSERT", "nonexistent", "INSERT INTO nonexistent VALUES (1)"},
		{"UPDATE", "locked", "UPDATE locked SET name = 'test'"},
		{"DELETE", "unknown", "DELETE FROM unknown WHERE id = 1"},
	}
	
	for _, q := range queries {
		fmt.Printf("Executing %s on table '%s'\n", q.operation, q.table)
		
		if err := queryDatabase(q.operation, q.table, q.query); err != nil {
			// Check if it's a DatabaseError
			if dbErr, ok := err.(DatabaseError); ok {
				fmt.Printf("❌ Database Error - Operation: %s, Table: %s\n",
					dbErr.Operation, dbErr.Table)
				fmt.Printf("   Cause: %v\n", dbErr.Cause)
				
				// Access the underlying error
				if underlying := dbErr.Unwrap(); underlying != nil {
					fmt.Printf("   Underlying error: %v\n", underlying)
				}
			} else {
				fmt.Printf("❌ Unknown error: %v\n", err)
			}
		} else {
			fmt.Println("✅ Database operation successful")
		}
		fmt.Println()
	}
	
	// BUSINESS LOGIC ERRORS
	fmt.Println("=== Business Logic Errors ===")
	
	orders := []struct {
		orderID string
		amount  float64
	}{
		{"ORD-001", 99.99},
		{"", 150.00},        // Empty order ID
		{"ORD-002", -50.00}, // Negative amount
		{"ORD-003", 15000.00}, // Amount too high
		{"ORD-004", 250.00}, // Valid order
	}
	
	for _, order := range orders {
		fmt.Printf("Processing order: ID='%s', Amount=%.2f\n", order.orderID, order.amount)
		
		if err := processOrder(order.orderID, order.amount); err != nil {
			// Check if it's a BusinessLogicError
			if businessErr, ok := err.(BusinessLogicError); ok {
				fmt.Printf("❌ Business Logic Error - Code: %s\n", businessErr.GetCode())
				fmt.Printf("   Message: %s\n", businessErr.Message)
				
				// Access additional details
				details := businessErr.GetDetails()
				if len(details) > 0 {
					fmt.Println("   Details:")
					for key, value := range details {
						fmt.Printf("     %s: %v\n", key, value)
					}
				}
			} else {
				fmt.Printf("❌ Unknown error: %v\n", err)
			}
		} else {
			fmt.Println("✅ Order processed successfully")
		}
		fmt.Println()
	}
	
	// ERROR TYPE CHECKING
	fmt.Println("=== Error Type Checking ===")
	
	// Function that returns different types of errors
	getError := func(errorType string) error {
		switch errorType {
		case "validation":
			return ValidationError{Field: "test", Value: "invalid", Message: "test error"}
		case "network":
			return NetworkError{URL: "test.com", StatusCode: 500, Timestamp: time.Now(), Retryable: true}
		case "database":
			return DatabaseError{Operation: "SELECT", Table: "test", Cause: fmt.Errorf("connection failed")}
		case "business":
			return BusinessLogicError{Code: "TEST_ERROR", Message: "test business error"}
		default:
			return nil
		}
	}
	
	errorTypes := []string{"validation", "network", "database", "business", "none"}
	
	for _, errorType := range errorTypes {
		err := getError(errorType)
		
		fmt.Printf("Error type: %s\n", errorType)
		
		if err == nil {
			fmt.Println("✅ No error")
		} else {
			fmt.Printf("Error: %v\n", err)
			
			// Type switch for different error types
			switch e := err.(type) {
			case ValidationError:
				fmt.Printf("   → Validation error in field: %s\n", e.Field)
			case NetworkError:
				fmt.Printf("   → Network error, retryable: %t\n", e.IsRetryable())
			case DatabaseError:
				fmt.Printf("   → Database error in operation: %s\n", e.Operation)
			case BusinessLogicError:
				fmt.Printf("   → Business error with code: %s\n", e.GetCode())
			default:
				fmt.Printf("   → Unknown error type\n")
			}
		}
		fmt.Println()
	}
	
	fmt.Println("Custom errors demonstration complete! 🎯")
	fmt.Println("Key takeaway: Custom errors provide structured, actionable error information!")
}
