// Package declaration
package main

// Import packages
import (
	"errors"
	"fmt"
	"strconv"
)

// ERROR HANDLING BASICS

// Function that returns an error
func divide(a, b float64) (float64, error) {
	if b == 0 {
		// Return zero value and an error
		return 0, errors.New("division by zero")
	}
	return a / b, nil // nil means no error
}

// Function that validates age
func validateAge(age int) error {
	if age < 0 {
		return errors.New("age cannot be negative")
	}
	if age > 150 {
		return errors.New("age seems unrealistic")
	}
	return nil // No error
}

// Function that parses a string to integer with error handling
func parseInteger(s string) (int, error) {
	// strconv.Atoi returns (int, error)
	num, err := strconv.Atoi(s)
	if err != nil {
		// Return zero value and the error
		return 0, fmt.Errorf("failed to parse '%s' as integer: %w", s, err)
	}
	return num, nil
}

// Function that demonstrates multiple error conditions
func processUser(name string, ageStr string) error {
	// Check name
	if name == "" {
		return errors.New("name cannot be empty")
	}
	
	// Parse age
	age, err := strconv.Atoi(ageStr)
	if err != nil {
		return fmt.Errorf("invalid age format: %w", err)
	}
	
	// Validate age
	if err := validateAge(age); err != nil {
		return fmt.Errorf("age validation failed: %w", err)
	}
	
	fmt.Printf("✅ User processed: %s (age %d)\n", name, age)
	return nil
}

// Function that simulates file operations
func readFile(filename string) (string, error) {
	// Simulate different error conditions
	switch filename {
	case "":
		return "", errors.New("filename cannot be empty")
	case "nonexistent.txt":
		return "", errors.New("file not found")
	case "locked.txt":
		return "", errors.New("permission denied")
	case "corrupted.txt":
		return "", errors.New("file is corrupted")
	default:
		return fmt.Sprintf("Contents of %s", filename), nil
	}
}

// Function that demonstrates error propagation
func processFile(filename string) error {
	fmt.Printf("📁 Processing file: %s\n", filename)
	
	// Try to read file
	content, err := readFile(filename)
	if err != nil {
		// Wrap the error with context
		return fmt.Errorf("failed to process file '%s': %w", filename, err)
	}
	
	fmt.Printf("📄 File content: %s\n", content)
	return nil
}

// Function that handles multiple operations
func performCalculations() error {
	calculations := []struct {
		a, b   float64
		name   string
	}{
		{10, 2, "normal division"},
		{15, 3, "another division"},
		{8, 0, "division by zero"},
		{20, 4, "final division"},
	}
	
	for _, calc := range calculations {
		result, err := divide(calc.a, calc.b)
		if err != nil {
			// Log error but continue with other calculations
			fmt.Printf("❌ Error in %s: %v\n", calc.name, err)
			continue
		}
		fmt.Printf("✅ %s: %.2f ÷ %.2f = %.2f\n", calc.name, calc.a, calc.b, result)
	}
	
	return nil
}

// Main function
func main() {
	fmt.Println("=== ERROR TYPES IN GO ===")
	fmt.Println()
	
	// BASIC ERROR HANDLING
	fmt.Println("=== Basic Error Handling ===")
	
	// Successful operation
	result, err := divide(10, 2)
	if err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	} else {
		fmt.Printf("✅ Result: %.2f\n", result)
	}
	
	// Operation that fails
	result, err = divide(10, 0)
	if err != nil {
		fmt.Printf("❌ Error: %v\n", err)
	} else {
		fmt.Printf("✅ Result: %.2f\n", result)
	}
	
	fmt.Println()
	
	// ERROR CHECKING PATTERNS
	fmt.Println("=== Error Checking Patterns ===")
	
	// Pattern 1: Check and handle immediately
	if err := validateAge(25); err != nil {
		fmt.Printf("❌ Age validation error: %v\n", err)
	} else {
		fmt.Println("✅ Age is valid")
	}
	
	// Pattern 2: Check and return early
	if err := validateAge(-5); err != nil {
		fmt.Printf("❌ Age validation error: %v\n", err)
	}
	
	// Pattern 3: Store error and check later
	err1 := validateAge(200)
	err2 := validateAge(30)
	
	if err1 != nil {
		fmt.Printf("❌ First validation error: %v\n", err1)
	}
	if err2 != nil {
		fmt.Printf("❌ Second validation error: %v\n", err2)
	} else {
		fmt.Println("✅ Second validation passed")
	}
	
	fmt.Println()
	
	// STRING PARSING WITH ERRORS
	fmt.Println("=== String Parsing with Errors ===")
	
	testStrings := []string{"123", "456", "abc", "789", ""}
	
	for _, s := range testStrings {
		num, err := parseInteger(s)
		if err != nil {
			fmt.Printf("❌ Parse error for '%s': %v\n", s, err)
		} else {
			fmt.Printf("✅ Parsed '%s' as %d\n", s, num)
		}
	}
	
	fmt.Println()
	
	// USER PROCESSING WITH MULTIPLE VALIDATIONS
	fmt.Println("=== User Processing ===")
	
	users := []struct {
		name string
		age  string
	}{
		{"Alice", "25"},
		{"", "30"},           // Empty name
		{"Bob", "abc"},       // Invalid age format
		{"Charlie", "-5"},    // Negative age
		{"Diana", "200"},     // Unrealistic age
		{"Eve", "28"},        // Valid user
	}
	
	for _, user := range users {
		fmt.Printf("Processing user: name='%s', age='%s'\n", user.name, user.age)
		if err := processUser(user.name, user.age); err != nil {
			fmt.Printf("❌ Error: %v\n", err)
		}
		fmt.Println()
	}
	
	// FILE PROCESSING WITH ERROR PROPAGATION
	fmt.Println("=== File Processing ===")
	
	files := []string{
		"document.txt",
		"",                    // Empty filename
		"nonexistent.txt",     // File not found
		"locked.txt",          // Permission denied
		"corrupted.txt",       // Corrupted file
		"valid_file.txt",      // Valid file
	}
	
	for _, filename := range files {
		if err := processFile(filename); err != nil {
			fmt.Printf("❌ File processing error: %v\n", err)
		}
		fmt.Println()
	}
	
	// MULTIPLE OPERATIONS WITH ERROR HANDLING
	fmt.Println("=== Multiple Calculations ===")
	
	if err := performCalculations(); err != nil {
		fmt.Printf("❌ Calculation error: %v\n", err)
	}
	
	fmt.Println()
	
	// ERROR COMPARISON
	fmt.Println("=== Error Comparison ===")
	
	// Create specific errors
	var divisionByZeroError = errors.New("division by zero")
	var fileNotFoundError = errors.New("file not found")
	
	// Function that returns specific errors
	testError := func(errorType string) error {
		switch errorType {
		case "division":
			return divisionByZeroError
		case "file":
			return fileNotFoundError
		default:
			return nil
		}
	}
	
	// Test error comparison
	err = testError("division")
	if err == divisionByZeroError {
		fmt.Println("✅ Detected division by zero error")
	}
	
	err = testError("file")
	if err == fileNotFoundError {
		fmt.Println("✅ Detected file not found error")
	}
	
	err = testError("none")
	if err == nil {
		fmt.Println("✅ No error occurred")
	}
	
	fmt.Println()
	
	// ERROR HANDLING BEST PRACTICES
	fmt.Println("=== Error Handling Best Practices ===")
	fmt.Println("1. Always check errors explicitly")
	fmt.Println("2. Handle errors close to where they occur")
	fmt.Println("3. Provide context when wrapping errors")
	fmt.Println("4. Use meaningful error messages")
	fmt.Println("5. Don't ignore errors (avoid _ = err)")
	fmt.Println("6. Return errors as the last return value")
	fmt.Println("7. Use fmt.Errorf for error wrapping")
	
	// DEMONSTRATION OF GOOD VS BAD ERROR HANDLING
	fmt.Println("\n=== Good vs Bad Error Handling ===")
	
	// BAD: Ignoring errors
	fmt.Println("❌ Bad: Ignoring errors")
	result, _ = divide(10, 0) // Ignoring error
	fmt.Printf("Result: %.2f (but we ignored a potential error!)\n", result)
	
	// GOOD: Proper error handling
	fmt.Println("\n✅ Good: Proper error handling")
	result, err = divide(10, 0)
	if err != nil {
		fmt.Printf("Cannot perform division: %v\n", err)
		// Handle the error appropriately
	} else {
		fmt.Printf("Division result: %.2f\n", result)
	}
	
	fmt.Println()
	fmt.Println("Error types demonstration complete! ⚠️")
	fmt.Println("Next: Learn about custom errors for more specific error handling!")
}
