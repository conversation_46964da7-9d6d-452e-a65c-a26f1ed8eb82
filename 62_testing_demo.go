// Package main demonstrates how to run tests and understand test output
package main

import (
	"fmt"
	"os"
	"os/exec"
	"strings"
)

// TESTING DEMONSTRATION

// Function to run a command and display output
func runTestCommand(name string, args ...string) error {
	fmt.Printf("Running: %s %s\n", name, fmt.Sprintf("%v", args))
	fmt.Println(strings.Repeat("-", 50))

	cmd := exec.Command(name, args...)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	err := cmd.Run()
	fmt.Println(strings.Repeat("-", 50))

	if err != nil {
		fmt.Printf("Command failed: %v\n", err)
	}

	fmt.Println()
	return err
}

// Function to demonstrate test commands
func demonstrateTestCommands() {
	fmt.Println("=== GO TESTING COMMANDS ===")
	fmt.Println()

	// Basic test commands
	commands := []struct {
		command     string
		description string
	}{
		{"go test", "Run all tests in current package"},
		{"go test -v", "Run tests with verbose output"},
		{"go test -run TestAdd", "Run specific test function"},
		{"go test -run TestValidate.*", "Run tests matching pattern"},
		{"go test ./...", "Run tests in all subdirectories"},
		{"go test -cover", "Run tests with coverage report"},
		{"go test -coverprofile=coverage.out", "Generate coverage profile"},
		{"go test -bench=.", "Run all benchmarks"},
		{"go test -bench=BenchmarkAdd", "Run specific benchmark"},
		{"go test -benchmem", "Include memory allocation stats"},
		{"go test -short", "Run tests with -short flag"},
		{"go test -timeout 30s", "Set test timeout"},
		{"go test -parallel 4", "Set parallel test limit"},
		{"go test -count=3", "Run each test 3 times"},
		{"go test -failfast", "Stop on first test failure"},
	}

	for _, cmd := range commands {
		fmt.Printf("%-35s - %s\n", cmd.command, cmd.description)
	}

	fmt.Println()
}

// Function to show test file structure
func showTestFileStructure() {
	fmt.Println("=== TEST FILE STRUCTURE ===")
	fmt.Println()

	fmt.Println("Test files must:")
	fmt.Println("1. End with '_test.go'")
	fmt.Println("2. Be in the same package as the code being tested")
	fmt.Println("3. Import the 'testing' package")
	fmt.Println()

	fmt.Println("Test functions must:")
	fmt.Println("1. Start with 'Test'")
	fmt.Println("2. Take a single parameter: t *testing.T")
	fmt.Println("3. Use t.Error, t.Errorf, t.Fatal, or t.Fatalf to report failures")
	fmt.Println()

	fmt.Println("Example test function:")
	fmt.Println(`func TestAdd(t *testing.T) {
    result := Add(2, 3)
    expected := 5
    
    if result != expected {
        t.Errorf("Add(2, 3) = %d; expected %d", result, expected)
    }
}`)
	fmt.Println()
}

// Function to show table-driven test patterns
func showTableDrivenPatterns() {
	fmt.Println("=== TABLE-DRIVEN TEST PATTERNS ===")
	fmt.Println()

	fmt.Println("Pattern 1: Slice of structs")
	fmt.Println(`testCases := []struct {
    input    int
    expected int
}{
    {2, 4},
    {3, 9},
    {4, 16},
}

for _, tc := range testCases {
    result := Square(tc.input)
    if result != tc.expected {
        t.Errorf("Square(%d) = %d; expected %d", tc.input, result, tc.expected)
    }
}`)
	fmt.Println()

	fmt.Println("Pattern 2: Map of test cases")
	fmt.Println(`tests := map[string]struct {
    input    string
    expected bool
}{
    "valid email":   {"<EMAIL>", true},
    "invalid email": {"invalid", false},
}

for name, tt := range tests {
    t.Run(name, func(t *testing.T) {
        result := IsValidEmail(tt.input)
        if result != tt.expected {
            t.Errorf("IsValidEmail(%s) = %t; expected %t", tt.input, result, tt.expected)
        }
    })
}`)
	fmt.Println()

	fmt.Println("Pattern 3: Subtests with t.Run")
	fmt.Println(`for _, tc := range testCases {
    t.Run(tc.name, func(t *testing.T) {
        // Test logic here
    })
}`)
	fmt.Println()
}

// Function to show testing best practices
func showTestingBestPractices() {
	fmt.Println("=== TESTING BEST PRACTICES ===")
	fmt.Println()

	practices := []string{
		"Write tests before or alongside your code (TDD)",
		"Use table-driven tests for multiple test cases",
		"Use descriptive test names that explain what's being tested",
		"Test both happy path and edge cases",
		"Test error conditions and boundary values",
		"Keep tests simple and focused on one thing",
		"Use t.Helper() in test helper functions",
		"Use testdata/ directory for test files",
		"Mock external dependencies",
		"Aim for high test coverage but focus on critical paths",
		"Use benchmarks for performance-critical code",
		"Run tests frequently during development",
		"Use -race flag to detect race conditions",
		"Write integration tests for complex workflows",
		"Document test setup and teardown requirements",
	}

	for i, practice := range practices {
		fmt.Printf("%d. %s\n", i+1, practice)
	}

	fmt.Println()
}

// Function to show test assertion patterns
func showAssertionPatterns() {
	fmt.Println("=== TEST ASSERTION PATTERNS ===")
	fmt.Println()

	fmt.Println("Basic assertions:")
	fmt.Println(`// Check equality
if result != expected {
    t.Errorf("got %v; expected %v", result, expected)
}

// Check error conditions
if err != nil {
    t.Errorf("unexpected error: %v", err)
}

if err == nil {
    t.Error("expected error but got none")
}

// Check boolean conditions
if !condition {
    t.Error("expected condition to be true")
}`)
	fmt.Println()

	fmt.Println("Advanced assertions:")
	fmt.Println(`// Check slice equality
if !reflect.DeepEqual(result, expected) {
    t.Errorf("got %v; expected %v", result, expected)
}

// Check string contains
if !strings.Contains(result, expected) {
    t.Errorf("result %q does not contain %q", result, expected)
}

// Check floating point equality
tolerance := 0.0001
if math.Abs(result-expected) > tolerance {
    t.Errorf("got %f; expected %f (tolerance %f)", result, expected, tolerance)
}`)
	fmt.Println()
}

// Function to show benchmark patterns
func showBenchmarkPatterns() {
	fmt.Println("=== BENCHMARK PATTERNS ===")
	fmt.Println()

	fmt.Println("Basic benchmark:")
	fmt.Println(`func BenchmarkFunction(b *testing.B) {
    for i := 0; i < b.N; i++ {
        Function(input)
    }
}`)
	fmt.Println()

	fmt.Println("Benchmark with setup:")
	fmt.Println(`func BenchmarkFunction(b *testing.B) {
    // Setup
    data := generateTestData()
    
    b.ResetTimer() // Don't include setup time
    
    for i := 0; i < b.N; i++ {
        Function(data)
    }
}`)
	fmt.Println()

	fmt.Println("Benchmark with memory allocation tracking:")
	fmt.Println(`func BenchmarkFunction(b *testing.B) {
    b.ReportAllocs() // Report memory allocations
    
    for i := 0; i < b.N; i++ {
        Function(input)
    }
}`)
	fmt.Println()
}

// Main function
func main() {
	fmt.Println("=== GO TESTING DEMONSTRATION ===")
	fmt.Println()

	// Show test commands
	demonstrateTestCommands()

	// Show test file structure
	showTestFileStructure()

	// Show table-driven patterns
	showTableDrivenPatterns()

	// Show assertion patterns
	showAssertionPatterns()

	// Show benchmark patterns
	showBenchmarkPatterns()

	// Show best practices
	showTestingBestPractices()

	// Test execution examples
	fmt.Println("=== RUNNING TESTS ===")
	fmt.Println()

	fmt.Println("To run the calculator tests:")
	fmt.Println("1. Save calculator.go and calculator_test.go in the same directory")
	fmt.Println("2. Run: go test -v")
	fmt.Println("3. Run benchmarks: go test -bench=.")
	fmt.Println("4. Run with coverage: go test -cover")
	fmt.Println()

	fmt.Println("To run the validation tests:")
	fmt.Println("1. Save validation.go and validation_test.go in the same directory")
	fmt.Println("2. Run: go test -v")
	fmt.Println("3. Run specific test: go test -run TestValidateEmail")
	fmt.Println("4. Run with coverage: go test -cover")
	fmt.Println()

	// Coverage analysis
	fmt.Println("=== COVERAGE ANALYSIS ===")
	fmt.Println()
	fmt.Println("Generate coverage report:")
	fmt.Println("go test -coverprofile=coverage.out")
	fmt.Println("go tool cover -html=coverage.out")
	fmt.Println()

	fmt.Println("Coverage by function:")
	fmt.Println("go test -coverprofile=coverage.out")
	fmt.Println("go tool cover -func=coverage.out")
	fmt.Println()

	// Test organization
	fmt.Println("=== TEST ORGANIZATION ===")
	fmt.Println()
	fmt.Println("Project structure with tests:")
	fmt.Println("myproject/")
	fmt.Println("├── main.go")
	fmt.Println("├── calculator/")
	fmt.Println("│   ├── calculator.go")
	fmt.Println("│   └── calculator_test.go")
	fmt.Println("├── validation/")
	fmt.Println("│   ├── validation.go")
	fmt.Println("│   └── validation_test.go")
	fmt.Println("└── testdata/")
	fmt.Println("    ├── input.json")
	fmt.Println("    └── expected.json")
	fmt.Println()

	fmt.Println("Testing demonstration complete! 🧪")
	fmt.Println("Key takeaway: Good tests make your code reliable and maintainable!")
}
