// Package declaration
package main

// Import packages
import (
	"fmt"
	"math/rand"
	"sync"
	"time"
)

// WAITGROUP EXAMPLES

// Function that simulates work
func doWork(workerID int, wg *sync.WaitGroup) {
	// Always call Done() when function exits
	defer wg.Done()
	
	workTime := time.Duration(rand.Intn(500)+100) * time.Millisecond
	
	fmt.Printf("👷 Worker %d: Starting work (will take %v)\n", workerID, workTime)
	time.Sleep(workTime) // Simulate work
	fmt.Printf("✅ Worker %d: Work completed!\n", workerID)
}

// Function that processes data
func processData(dataID int, data string, wg *sync.WaitGroup) {
	defer wg.Done()
	
	fmt.Printf("🔄 Processing data %d: %s\n", dataID, data)
	
	// Simulate processing time
	processingTime := time.Duration(rand.Intn(300)+50) * time.Millisecond
	time.Sleep(processingTime)
	
	fmt.Printf("✅ Data %d processed successfully\n", dataID)
}

// Function that downloads a file
func downloadFile(fileID int, filename string, wg *sync.WaitGroup) {
	defer wg.Done()
	
	fmt.Printf("📥 Starting download %d: %s\n", fileID, filename)
	
	// Simulate download with progress
	downloadTime := time.Duration(rand.Intn(400)+100) * time.Millisecond
	steps := 5
	stepTime := downloadTime / time.Duration(steps)
	
	for i := 1; i <= steps; i++ {
		time.Sleep(stepTime)
		progress := (i * 100) / steps
		fmt.Printf("📥 Download %d: %d%% complete\n", fileID, progress)
	}
	
	fmt.Printf("✅ Download %d completed: %s\n", fileID, filename)
}

// Function that demonstrates WaitGroup with error handling
func riskyWork(workerID int, wg *sync.WaitGroup, results chan<- string) {
	defer wg.Done()
	
	fmt.Printf("⚠️ Risky worker %d: Starting...\n", workerID)
	
	// Simulate work that might fail
	workTime := time.Duration(rand.Intn(200)+50) * time.Millisecond
	time.Sleep(workTime)
	
	// 20% chance of failure
	if rand.Float32() < 0.2 {
		results <- fmt.Sprintf("❌ Worker %d: Failed", workerID)
		return
	}
	
	results <- fmt.Sprintf("✅ Worker %d: Success", workerID)
}

// Function that demonstrates nested WaitGroups
func coordinateTeams(teamID int, wg *sync.WaitGroup) {
	defer wg.Done()
	
	fmt.Printf("👥 Team %d: Starting coordination\n", teamID)
	
	// Create WaitGroup for team members
	var teamWG sync.WaitGroup
	teamSize := 3
	
	// Start team members
	for memberID := 1; memberID <= teamSize; memberID++ {
		teamWG.Add(1)
		go func(tID, mID int) {
			defer teamWG.Done()
			workTime := time.Duration(rand.Intn(200)+50) * time.Millisecond
			fmt.Printf("👤 Team %d, Member %d: Working for %v\n", tID, mID, workTime)
			time.Sleep(workTime)
			fmt.Printf("✅ Team %d, Member %d: Done\n", tID, mID)
		}(teamID, memberID)
	}
	
	// Wait for all team members to finish
	teamWG.Wait()
	fmt.Printf("👥 Team %d: All members finished\n", teamID)
}

// Main function
func main() {
	fmt.Println("=== WAITGROUPS IN GO ===")
	fmt.Println()
	
	// Seed random number generator
	rand.Seed(time.Now().UnixNano())
	
	// BASIC WAITGROUP USAGE
	fmt.Println("=== Basic WaitGroup Usage ===")
	
	var wg sync.WaitGroup
	numWorkers := 3
	
	// Add the number of goroutines to wait for
	wg.Add(numWorkers)
	
	// Start workers
	for i := 1; i <= numWorkers; i++ {
		go doWork(i, &wg)
	}
	
	fmt.Println("Main: Waiting for all workers to complete...")
	wg.Wait() // Block until all goroutines call Done()
	fmt.Println("Main: All workers completed!")
	
	fmt.Println()
	
	// WAITGROUP WITH DATA PROCESSING
	fmt.Println("=== WaitGroup with Data Processing ===")
	
	data := []string{
		"user_data.json",
		"sales_report.csv",
		"inventory.xml",
		"logs.txt",
		"config.yaml",
	}
	
	var processingWG sync.WaitGroup
	
	// Process all data files concurrently
	for i, item := range data {
		processingWG.Add(1)
		go processData(i+1, item, &processingWG)
	}
	
	fmt.Println("Main: Processing all data files...")
	processingWG.Wait()
	fmt.Println("Main: All data processing completed!")
	
	fmt.Println()
	
	// WAITGROUP WITH FILE DOWNLOADS
	fmt.Println("=== WaitGroup with File Downloads ===")
	
	files := []string{
		"document.pdf",
		"image.jpg",
		"video.mp4",
		"archive.zip",
	}
	
	var downloadWG sync.WaitGroup
	
	fmt.Println("Starting concurrent downloads...")
	
	for i, filename := range files {
		downloadWG.Add(1)
		go downloadFile(i+1, filename, &downloadWG)
	}
	
	downloadWG.Wait()
	fmt.Println("All downloads completed!")
	
	fmt.Println()
	
	// WAITGROUP WITH ERROR HANDLING
	fmt.Println("=== WaitGroup with Error Handling ===")
	
	var errorWG sync.WaitGroup
	results := make(chan string, 5) // Buffered channel for results
	
	// Start risky workers
	for i := 1; i <= 5; i++ {
		errorWG.Add(1)
		go riskyWork(i, &errorWG, results)
	}
	
	// Wait for all workers in a separate goroutine
	go func() {
		errorWG.Wait()
		close(results) // Close channel when all workers are done
	}()
	
	// Collect results
	fmt.Println("Collecting results from risky workers:")
	for result := range results {
		fmt.Println(result)
	}
	
	fmt.Println()
	
	// NESTED WAITGROUPS
	fmt.Println("=== Nested WaitGroups ===")
	
	var mainWG sync.WaitGroup
	numTeams := 2
	
	// Start teams
	for teamID := 1; teamID <= numTeams; teamID++ {
		mainWG.Add(1)
		go coordinateTeams(teamID, &mainWG)
	}
	
	fmt.Println("Main: Waiting for all teams to complete...")
	mainWG.Wait()
	fmt.Println("Main: All teams completed their work!")
	
	fmt.Println()
	
	// WAITGROUP WITH TIMEOUT
	fmt.Println("=== WaitGroup with Timeout ===")
	
	var timeoutWG sync.WaitGroup
	done := make(chan bool)
	
	// Start some work
	for i := 1; i <= 3; i++ {
		timeoutWG.Add(1)
		go func(workerID int) {
			defer timeoutWG.Done()
			// Some workers take longer
			workTime := time.Duration(workerID*200) * time.Millisecond
			fmt.Printf("⏱️ Timeout worker %d: Working for %v\n", workerID, workTime)
			time.Sleep(workTime)
			fmt.Printf("✅ Timeout worker %d: Completed\n", workerID)
		}(i)
	}
	
	// Wait for WaitGroup in a separate goroutine
	go func() {
		timeoutWG.Wait()
		done <- true
	}()
	
	// Wait with timeout
	select {
	case <-done:
		fmt.Println("✅ All workers completed within timeout")
	case <-time.After(500 * time.Millisecond):
		fmt.Println("⏰ Timeout: Some workers are still running")
	}
	
	// Give remaining workers time to finish
	time.Sleep(200 * time.Millisecond)
	
	fmt.Println()
	
	// PRACTICAL EXAMPLE: BATCH PROCESSING
	fmt.Println("=== Batch Processing Example ===")
	
	// Simulate processing a batch of items
	batchSize := 10
	maxConcurrency := 3
	
	var batchWG sync.WaitGroup
	semaphore := make(chan struct{}, maxConcurrency) // Limit concurrency
	
	fmt.Printf("Processing batch of %d items with max %d concurrent workers\n", 
		batchSize, maxConcurrency)
	
	for i := 1; i <= batchSize; i++ {
		batchWG.Add(1)
		
		go func(itemID int) {
			defer batchWG.Done()
			
			// Acquire semaphore (limit concurrency)
			semaphore <- struct{}{}
			defer func() { <-semaphore }() // Release semaphore
			
			fmt.Printf("🔄 Processing item %d\n", itemID)
			time.Sleep(time.Duration(rand.Intn(100)+50) * time.Millisecond)
			fmt.Printf("✅ Item %d processed\n", itemID)
		}(i)
	}
	
	batchWG.Wait()
	fmt.Println("Batch processing completed!")
	
	fmt.Println()
	
	// WAITGROUP BEST PRACTICES
	fmt.Println("=== WaitGroup Best Practices ===")
	fmt.Println("1. Always call wg.Add() before starting goroutines")
	fmt.Println("2. Always call wg.Done() when goroutine finishes (use defer)")
	fmt.Println("3. Pass WaitGroup by pointer (&wg)")
	fmt.Println("4. Don't copy WaitGroup values")
	fmt.Println("5. Consider using channels for complex coordination")
	fmt.Println("6. Use timeouts for long-running operations")
	fmt.Println("7. Handle errors appropriately in concurrent code")
	
	fmt.Println()
	fmt.Println("WaitGroups demonstration complete! ⏳")
	fmt.Println("Key takeaway: WaitGroups ensure all concurrent work completes!")
}
