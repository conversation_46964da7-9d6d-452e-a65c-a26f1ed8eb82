// Test file demonstrating table-driven testing patterns
package validation

import (
	"errors"
	"fmt"
	"strings"
	"testing"
)

// TABLE-DRIVEN TESTS

// TestValidateEmail demonstrates basic table-driven testing
func TestValidateEmail(t *testing.T) {
	// Define test cases as a slice of structs
	testCases := []struct {
		email       string
		expectError bool
		description string
	}{
		{"<EMAIL>", false, "valid email"},
		{"<EMAIL>", false, "valid email with subdomain"},
		{"<EMAIL>", false, "valid email with plus sign"},
		{"", true, "empty email"},
		{"invalid-email", true, "missing @ symbol"},
		{"@example.com", true, "missing username"},
		{"user@", true, "missing domain"},
		{"user@domain", true, "missing TLD"},
		{"user <EMAIL>", true, "space in username"},
	}

	// Iterate through test cases
	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			err := ValidateEmail(tc.email)

			if tc.expectError && err == nil {
				t.Errorf("Expected error for email '%s', but got none", tc.email)
			}

			if !tc.expectError && err != nil {
				t.Errorf("Expected no error for email '%s', but got: %v", tc.email, err)
			}
		})
	}
}

// TestValidatePassword demonstrates complex table-driven testing
func TestValidatePassword(t *testing.T) {
	tests := []struct {
		name          string
		password      string
		expectError   bool
		errorContains string // Expected error message substring
	}{
		{
			name:        "valid password",
			password:    "MySecure123!",
			expectError: false,
		},
		{
			name:          "too short",
			password:      "Short1!",
			expectError:   true,
			errorContains: "at least 8 characters",
		},
		{
			name:          "too long",
			password:      strings.Repeat("a", 129) + "A1!",
			expectError:   true,
			errorContains: "less than 128 characters",
		},
		{
			name:          "no uppercase",
			password:      "lowercase123!",
			expectError:   true,
			errorContains: "uppercase letter",
		},
		{
			name:          "no lowercase",
			password:      "UPPERCASE123!",
			expectError:   true,
			errorContains: "lowercase letter",
		},
		{
			name:          "no digit",
			password:      "NoDigitHere!",
			expectError:   true,
			errorContains: "digit",
		},
		{
			name:          "no special character",
			password:      "NoSpecialChar123",
			expectError:   true,
			errorContains: "special character",
		},
		{
			name:        "minimum valid password",
			password:    "Aa1!bcde",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidatePassword(tt.password)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for password '%s', but got none", tt.password)
				} else if tt.errorContains != "" && !strings.Contains(err.Error(), tt.errorContains) {
					t.Errorf("Expected error to contain '%s', but got: %v", tt.errorContains, err)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error for password '%s', but got: %v", tt.password, err)
				}
			}
		})
	}
}

// TestValidateAge demonstrates simple table-driven testing with ranges
func TestValidateAge(t *testing.T) {
	testCases := []struct {
		age         int
		expectError bool
	}{
		{0, false},   // Valid: minimum age
		{25, false},  // Valid: normal age
		{150, false}, // Valid: maximum age
		{-1, true},   // Invalid: negative age
		{151, true},  // Invalid: too old
		{-100, true}, // Invalid: very negative
		{1000, true}, // Invalid: unrealistic age
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("age_%d", tc.age), func(t *testing.T) {
			err := ValidateAge(tc.age)

			if tc.expectError && err == nil {
				t.Errorf("Expected error for age %d, but got none", tc.age)
			}

			if !tc.expectError && err != nil {
				t.Errorf("Expected no error for age %d, but got: %v", tc.age, err)
			}
		})
	}
}

// TestValidateUsername demonstrates table-driven testing with detailed cases
func TestValidateUsername(t *testing.T) {
	tests := map[string]struct {
		username    string
		expectError bool
		description string
	}{
		"valid_simple": {
			username:    "john_doe",
			expectError: false,
			description: "simple valid username",
		},
		"valid_with_numbers": {
			username:    "user123",
			expectError: false,
			description: "username with numbers",
		},
		"too_short": {
			username:    "ab",
			expectError: true,
			description: "username too short",
		},
		"too_long": {
			username:    "this_username_is_way_too_long_for_validation",
			expectError: true,
			description: "username too long",
		},
		"starts_with_number": {
			username:    "123user",
			expectError: true,
			description: "username starts with number",
		},
		"contains_special_chars": {
			username:    "user@name",
			expectError: true,
			description: "username contains special characters",
		},
		"contains_spaces": {
			username:    "user name",
			expectError: true,
			description: "username contains spaces",
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			err := ValidateUsername(tt.username)

			if tt.expectError && err == nil {
				t.Errorf("Expected error for username '%s' (%s), but got none",
					tt.username, tt.description)
			}

			if !tt.expectError && err != nil {
				t.Errorf("Expected no error for username '%s' (%s), but got: %v",
					tt.username, tt.description, err)
			}
		})
	}
}

// TestValidatePhoneNumber demonstrates testing with data transformation
func TestValidatePhoneNumber(t *testing.T) {
	testCases := []struct {
		phone       string
		expectError bool
		description string
	}{
		{"1234567890", false, "plain 10 digits"},
		{"************", false, "with dashes"},
		{"(*************", false, "with parentheses and spaces"},
		{"****** 456 7890", false, "with country code"},
		{"123456789", true, "too short"},
		{"12345678901", true, "too long"},
		{"123-456-789a", true, "contains letter"},
		{"", true, "empty string"},
		{"abc-def-ghij", true, "all letters"},
	}

	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			err := ValidatePhoneNumber(tc.phone)

			if tc.expectError && err == nil {
				t.Errorf("Expected error for phone '%s', but got none", tc.phone)
			}

			if !tc.expectError && err != nil {
				t.Errorf("Expected no error for phone '%s', but got: %v", tc.phone, err)
			}
		})
	}
}

// TestValidateCreditCard demonstrates testing complex algorithms
func TestValidateCreditCard(t *testing.T) {
	tests := []struct {
		name        string
		cardNumber  string
		expectError bool
	}{
		{"valid Visa", "****************", false},
		{"valid MasterCard", "****************", false},
		{"valid with spaces", "4532 0151 1283 0366", false},
		{"valid with dashes", "4532-0151-1283-0366", false},
		{"invalid Luhn", "****************", true},
		{"too short", "123456789", true},
		{"too long", "12345678901234567890", true},
		{"contains letters", "4532015112830abc", true},
		{"empty", "", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateCreditCard(tt.cardNumber)

			if tt.expectError && err == nil {
				t.Errorf("Expected error for card number '%s', but got none", tt.cardNumber)
			}

			if !tt.expectError && err != nil {
				t.Errorf("Expected no error for card number '%s', but got: %v", tt.cardNumber, err)
			}
		})
	}
}

// TestValidateUser demonstrates testing functions that return multiple errors
func TestValidateUser(t *testing.T) {
	testCases := []struct {
		name           string
		user           User
		expectedErrors int
		description    string
	}{
		{
			name: "valid user",
			user: User{
				Username: "john_doe",
				Email:    "<EMAIL>",
				Age:      25,
				Phone:    "************",
			},
			expectedErrors: 0,
			description:    "all fields valid",
		},
		{
			name: "invalid username and email",
			user: User{
				Username: "ab",            // Too short
				Email:    "invalid-email", // Invalid format
				Age:      25,
				Phone:    "************",
			},
			expectedErrors: 2,
			description:    "username too short, email invalid",
		},
		{
			name: "all fields invalid",
			user: User{
				Username: "123invalid", // Starts with number
				Email:    "",           // Empty
				Age:      -5,           // Negative
				Phone:    "invalid",    // Invalid format
			},
			expectedErrors: 4,
			description:    "all fields have validation errors",
		},
		{
			name: "optional phone field empty",
			user: User{
				Username: "valid_user",
				Email:    "<EMAIL>",
				Age:      30,
				Phone:    "", // Empty but optional
			},
			expectedErrors: 0,
			description:    "phone field is optional when empty",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			errors := ValidateUser(tc.user)

			if len(errors) != tc.expectedErrors {
				t.Errorf("Expected %d errors for user (%s), but got %d: %v",
					tc.expectedErrors, tc.description, len(errors), errors)
			}
		})
	}
}

// HELPER FUNCTIONS FOR TESTING

// Helper function to check if error contains specific text
func errorContains(err error, text string) bool {
	if err == nil {
		return text == ""
	}
	return strings.Contains(err.Error(), text)
}

// TestHelperFunction demonstrates testing helper functions
func TestHelperFunction(t *testing.T) {
	testCases := []struct {
		err      error
		text     string
		expected bool
	}{
		{nil, "", true},
		{nil, "some text", false},
		{errors.New("test error"), "test", true},
		{errors.New("test error"), "missing", false},
	}

	for _, tc := range testCases {
		result := errorContains(tc.err, tc.text)
		if result != tc.expected {
			t.Errorf("errorContains(%v, %s) = %t; expected %t",
				tc.err, tc.text, result, tc.expected)
		}
	}
}

// BENCHMARK TESTS

// BenchmarkValidateEmail benchmarks email validation
func BenchmarkValidateEmail(b *testing.B) {
	email := "<EMAIL>"

	for i := 0; i < b.N; i++ {
		ValidateEmail(email)
	}
}

// BenchmarkValidatePassword benchmarks password validation
func BenchmarkValidatePassword(b *testing.B) {
	password := "MySecure123!"

	for i := 0; i < b.N; i++ {
		ValidatePassword(password)
	}
}

// BenchmarkValidateCreditCard benchmarks credit card validation
func BenchmarkValidateCreditCard(b *testing.B) {
	cardNumber := "****************"

	for i := 0; i < b.N; i++ {
		ValidateCreditCard(cardNumber)
	}
}
