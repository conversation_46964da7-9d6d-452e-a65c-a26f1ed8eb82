// File Processing Tool - A complete example demonstrating file I/O and data processing
package main

import (
	"bufio"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

// DATA STRUCTURES

// SalesRecord represents a sales transaction
type SalesRecord struct {
	ID         int     `json:"id" csv:"id"`
	Date       string  `json:"date" csv:"date"`
	Product    string  `json:"product" csv:"product"`
	Category   string  `json:"category" csv:"category"`
	Quantity   int     `json:"quantity" csv:"quantity"`
	Price      float64 `json:"price" csv:"price"`
	Total      float64 `json:"total" csv:"total"`
	Salesperson string `json:"salesperson" csv:"salesperson"`
	Region     string  `json:"region" csv:"region"`
}

// SalesAnalysis contains analysis results
type SalesAnalysis struct {
	TotalRecords    int                    `json:"total_records"`
	TotalRevenue    float64                `json:"total_revenue"`
	AverageOrderValue float64              `json:"average_order_value"`
	TopProducts     []ProductSummary       `json:"top_products"`
	TopSalespersons []SalespersonSummary   `json:"top_salespersons"`
	RegionSummary   map[string]RegionStats `json:"region_summary"`
	CategorySummary map[string]CategoryStats `json:"category_summary"`
	ProcessingTime  time.Duration          `json:"processing_time"`
}

// ProductSummary contains product sales summary
type ProductSummary struct {
	Product      string  `json:"product"`
	TotalSales   float64 `json:"total_sales"`
	TotalQuantity int    `json:"total_quantity"`
	OrderCount   int     `json:"order_count"`
}

// SalespersonSummary contains salesperson performance
type SalespersonSummary struct {
	Salesperson string  `json:"salesperson"`
	TotalSales  float64 `json:"total_sales"`
	OrderCount  int     `json:"order_count"`
	Region      string  `json:"region"`
}

// RegionStats contains regional statistics
type RegionStats struct {
	TotalSales   float64 `json:"total_sales"`
	OrderCount   int     `json:"order_count"`
	TopProduct   string  `json:"top_product"`
	Salespersons int     `json:"salespersons"`
}

// CategoryStats contains category statistics
type CategoryStats struct {
	TotalSales    float64 `json:"total_sales"`
	TotalQuantity int     `json:"total_quantity"`
	OrderCount    int     `json:"order_count"`
	AveragePrice  float64 `json:"average_price"`
}

// FILE PROCESSING

// FileProcessor handles file operations and analysis
type FileProcessor struct {
	inputFile  string
	outputDir  string
	records    []SalesRecord
	analysis   SalesAnalysis
	mutex      sync.RWMutex
}

// NewFileProcessor creates a new file processor
func NewFileProcessor(inputFile, outputDir string) *FileProcessor {
	return &FileProcessor{
		inputFile: inputFile,
		outputDir: outputDir,
		records:   []SalesRecord{},
	}
}

// CreateSampleData creates sample CSV data for demonstration
func (fp *FileProcessor) CreateSampleData() error {
	fmt.Println("Creating sample sales data...")
	
	// Sample data
	sampleData := [][]string{
		{"id", "date", "product", "category", "quantity", "price", "total", "salesperson", "region"},
		{"1", "2024-01-15", "Laptop Pro", "Electronics", "2", "1299.99", "2599.98", "Alice Johnson", "North"},
		{"2", "2024-01-16", "Wireless Mouse", "Electronics", "5", "29.99", "149.95", "Bob Smith", "South"},
		{"3", "2024-01-17", "Office Chair", "Furniture", "1", "299.99", "299.99", "Charlie Brown", "East"},
		{"4", "2024-01-18", "Smartphone", "Electronics", "3", "699.99", "2099.97", "Alice Johnson", "North"},
		{"5", "2024-01-19", "Desk Lamp", "Furniture", "4", "49.99", "199.96", "Diana Prince", "West"},
		{"6", "2024-01-20", "Tablet", "Electronics", "2", "399.99", "799.98", "Bob Smith", "South"},
		{"7", "2024-01-21", "Bookshelf", "Furniture", "1", "199.99", "199.99", "Charlie Brown", "East"},
		{"8", "2024-01-22", "Headphones", "Electronics", "6", "79.99", "479.94", "Diana Prince", "West"},
		{"9", "2024-01-23", "Standing Desk", "Furniture", "1", "599.99", "599.99", "Alice Johnson", "North"},
		{"10", "2024-01-24", "Monitor", "Electronics", "2", "249.99", "499.98", "Bob Smith", "South"},
	}
	
	// Create input file
	file, err := os.Create(fp.inputFile)
	if err != nil {
		return fmt.Errorf("failed to create sample file: %w", err)
	}
	defer file.Close()
	
	writer := csv.NewWriter(file)
	defer writer.Flush()
	
	for _, record := range sampleData {
		if err := writer.Write(record); err != nil {
			return fmt.Errorf("failed to write CSV record: %w", err)
		}
	}
	
	fmt.Printf("✅ Created sample data file: %s\n", fp.inputFile)
	return nil
}

// ReadCSVFile reads and parses the CSV file
func (fp *FileProcessor) ReadCSVFile() error {
	fmt.Printf("Reading CSV file: %s\n", fp.inputFile)
	
	file, err := os.Open(fp.inputFile)
	if err != nil {
		return fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()
	
	reader := csv.NewReader(file)
	
	// Read header
	header, err := reader.Read()
	if err != nil {
		return fmt.Errorf("failed to read header: %w", err)
	}
	
	fmt.Printf("CSV columns: %v\n", header)
	
	// Read records
	recordCount := 0
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("failed to read record: %w", err)
		}
		
		// Parse record
		salesRecord, err := fp.parseCSVRecord(record)
		if err != nil {
			fmt.Printf("Warning: Failed to parse record %d: %v\n", recordCount+1, err)
			continue
		}
		
		fp.records = append(fp.records, salesRecord)
		recordCount++
	}
	
	fmt.Printf("✅ Read %d records successfully\n", len(fp.records))
	return nil
}

// parseCSVRecord converts CSV record to SalesRecord
func (fp *FileProcessor) parseCSVRecord(record []string) (SalesRecord, error) {
	if len(record) < 9 {
		return SalesRecord{}, fmt.Errorf("insufficient columns")
	}
	
	id, err := strconv.Atoi(record[0])
	if err != nil {
		return SalesRecord{}, fmt.Errorf("invalid ID: %w", err)
	}
	
	quantity, err := strconv.Atoi(record[4])
	if err != nil {
		return SalesRecord{}, fmt.Errorf("invalid quantity: %w", err)
	}
	
	price, err := strconv.ParseFloat(record[5], 64)
	if err != nil {
		return SalesRecord{}, fmt.Errorf("invalid price: %w", err)
	}
	
	total, err := strconv.ParseFloat(record[6], 64)
	if err != nil {
		return SalesRecord{}, fmt.Errorf("invalid total: %w", err)
	}
	
	return SalesRecord{
		ID:         id,
		Date:       record[1],
		Product:    record[2],
		Category:   record[3],
		Quantity:   quantity,
		Price:      price,
		Total:      total,
		Salesperson: record[7],
		Region:     record[8],
	}, nil
}

// ANALYSIS FUNCTIONS

// AnalyzeData performs comprehensive data analysis
func (fp *FileProcessor) AnalyzeData() error {
	fmt.Println("Analyzing sales data...")
	start := time.Now()
	
	fp.mutex.Lock()
	defer fp.mutex.Unlock()
	
	// Initialize analysis
	fp.analysis = SalesAnalysis{
		TotalRecords:    len(fp.records),
		RegionSummary:   make(map[string]RegionStats),
		CategorySummary: make(map[string]CategoryStats),
	}
	
	// Calculate basic metrics
	fp.calculateBasicMetrics()
	
	// Analyze products
	fp.analyzeProducts()
	
	// Analyze salespersons
	fp.analyzeSalespersons()
	
	// Analyze regions
	fp.analyzeRegions()
	
	// Analyze categories
	fp.analyzeCategories()
	
	fp.analysis.ProcessingTime = time.Since(start)
	
	fmt.Printf("✅ Analysis completed in %v\n", fp.analysis.ProcessingTime)
	return nil
}

// calculateBasicMetrics calculates total revenue and average order value
func (fp *FileProcessor) calculateBasicMetrics() {
	totalRevenue := 0.0
	
	for _, record := range fp.records {
		totalRevenue += record.Total
	}
	
	fp.analysis.TotalRevenue = totalRevenue
	
	if fp.analysis.TotalRecords > 0 {
		fp.analysis.AverageOrderValue = totalRevenue / float64(fp.analysis.TotalRecords)
	}
}

// analyzeProducts analyzes product performance
func (fp *FileProcessor) analyzeProducts() {
	productMap := make(map[string]*ProductSummary)
	
	for _, record := range fp.records {
		if summary, exists := productMap[record.Product]; exists {
			summary.TotalSales += record.Total
			summary.TotalQuantity += record.Quantity
			summary.OrderCount++
		} else {
			productMap[record.Product] = &ProductSummary{
				Product:      record.Product,
				TotalSales:   record.Total,
				TotalQuantity: record.Quantity,
				OrderCount:   1,
			}
		}
	}
	
	// Convert to slice and sort by total sales
	for _, summary := range productMap {
		fp.analysis.TopProducts = append(fp.analysis.TopProducts, *summary)
	}
	
	sort.Slice(fp.analysis.TopProducts, func(i, j int) bool {
		return fp.analysis.TopProducts[i].TotalSales > fp.analysis.TopProducts[j].TotalSales
	})
	
	// Keep only top 5
	if len(fp.analysis.TopProducts) > 5 {
		fp.analysis.TopProducts = fp.analysis.TopProducts[:5]
	}
}

// analyzeSalespersons analyzes salesperson performance
func (fp *FileProcessor) analyzeSalespersons() {
	salespersonMap := make(map[string]*SalespersonSummary)
	
	for _, record := range fp.records {
		if summary, exists := salespersonMap[record.Salesperson]; exists {
			summary.TotalSales += record.Total
			summary.OrderCount++
		} else {
			salespersonMap[record.Salesperson] = &SalespersonSummary{
				Salesperson: record.Salesperson,
				TotalSales:  record.Total,
				OrderCount:  1,
				Region:      record.Region,
			}
		}
	}
	
	// Convert to slice and sort by total sales
	for _, summary := range salespersonMap {
		fp.analysis.TopSalespersons = append(fp.analysis.TopSalespersons, *summary)
	}
	
	sort.Slice(fp.analysis.TopSalespersons, func(i, j int) bool {
		return fp.analysis.TopSalespersons[i].TotalSales > fp.analysis.TopSalespersons[j].TotalSales
	})
}

// analyzeRegions analyzes regional performance
func (fp *FileProcessor) analyzeRegions() {
	regionProducts := make(map[string]map[string]float64)
	salespersonCount := make(map[string]map[string]bool)
	
	for _, record := range fp.records {
		// Initialize region if not exists
		if _, exists := fp.analysis.RegionSummary[record.Region]; !exists {
			fp.analysis.RegionSummary[record.Region] = RegionStats{}
			regionProducts[record.Region] = make(map[string]float64)
			salespersonCount[record.Region] = make(map[string]bool)
		}
		
		// Update region stats
		stats := fp.analysis.RegionSummary[record.Region]
		stats.TotalSales += record.Total
		stats.OrderCount++
		fp.analysis.RegionSummary[record.Region] = stats
		
		// Track products by region
		regionProducts[record.Region][record.Product] += record.Total
		
		// Track unique salespersons
		salespersonCount[record.Region][record.Salesperson] = true
	}
	
	// Find top product for each region
	for region, products := range regionProducts {
		topProduct := ""
		maxSales := 0.0
		
		for product, sales := range products {
			if sales > maxSales {
				maxSales = sales
				topProduct = product
			}
		}
		
		stats := fp.analysis.RegionSummary[region]
		stats.TopProduct = topProduct
		stats.Salespersons = len(salespersonCount[region])
		fp.analysis.RegionSummary[region] = stats
	}
}

// analyzeCategories analyzes category performance
func (fp *FileProcessor) analyzeCategories() {
	for _, record := range fp.records {
		if stats, exists := fp.analysis.CategorySummary[record.Category]; exists {
			stats.TotalSales += record.Total
			stats.TotalQuantity += record.Quantity
			stats.OrderCount++
			fp.analysis.CategorySummary[record.Category] = stats
		} else {
			fp.analysis.CategorySummary[record.Category] = CategoryStats{
				TotalSales:    record.Total,
				TotalQuantity: record.Quantity,
				OrderCount:    1,
			}
		}
	}
	
	// Calculate average prices
	for category, stats := range fp.analysis.CategorySummary {
		if stats.TotalQuantity > 0 {
			stats.AveragePrice = stats.TotalSales / float64(stats.TotalQuantity)
			fp.analysis.CategorySummary[category] = stats
		}
	}
}

// OUTPUT FUNCTIONS

// GenerateReports creates various output reports
func (fp *FileProcessor) GenerateReports() error {
	fmt.Println("Generating reports...")
	
	// Create output directory
	if err := os.MkdirAll(fp.outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}
	
	// Generate JSON report
	if err := fp.generateJSONReport(); err != nil {
		return fmt.Errorf("failed to generate JSON report: %w", err)
	}
	
	// Generate text summary
	if err := fp.generateTextSummary(); err != nil {
		return fmt.Errorf("failed to generate text summary: %w", err)
	}
	
	// Generate CSV summary
	if err := fp.generateCSVSummary(); err != nil {
		return fmt.Errorf("failed to generate CSV summary: %w", err)
	}
	
	fmt.Println("✅ All reports generated successfully")
	return nil
}

// generateJSONReport creates a detailed JSON report
func (fp *FileProcessor) generateJSONReport() error {
	filename := filepath.Join(fp.outputDir, "analysis_report.json")
	
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()
	
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	
	if err := encoder.Encode(fp.analysis); err != nil {
		return err
	}
	
	fmt.Printf("📄 JSON report: %s\n", filename)
	return nil
}

// generateTextSummary creates a human-readable summary
func (fp *FileProcessor) generateTextSummary() error {
	filename := filepath.Join(fp.outputDir, "summary.txt")
	
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()
	
	writer := bufio.NewWriter(file)
	defer writer.Flush()
	
	// Write summary
	fmt.Fprintf(writer, "SALES ANALYSIS SUMMARY\n")
	fmt.Fprintf(writer, "======================\n\n")
	fmt.Fprintf(writer, "Processing Time: %v\n", fp.analysis.ProcessingTime)
	fmt.Fprintf(writer, "Total Records: %d\n", fp.analysis.TotalRecords)
	fmt.Fprintf(writer, "Total Revenue: $%.2f\n", fp.analysis.TotalRevenue)
	fmt.Fprintf(writer, "Average Order Value: $%.2f\n\n", fp.analysis.AverageOrderValue)
	
	// Top products
	fmt.Fprintf(writer, "TOP PRODUCTS:\n")
	for i, product := range fp.analysis.TopProducts {
		fmt.Fprintf(writer, "%d. %s - $%.2f (%d units, %d orders)\n",
			i+1, product.Product, product.TotalSales, product.TotalQuantity, product.OrderCount)
	}
	
	// Top salespersons
	fmt.Fprintf(writer, "\nTOP SALESPERSONS:\n")
	for i, person := range fp.analysis.TopSalespersons {
		fmt.Fprintf(writer, "%d. %s (%s) - $%.2f (%d orders)\n",
			i+1, person.Salesperson, person.Region, person.TotalSales, person.OrderCount)
	}
	
	// Regional summary
	fmt.Fprintf(writer, "\nREGIONAL SUMMARY:\n")
	for region, stats := range fp.analysis.RegionSummary {
		fmt.Fprintf(writer, "%s: $%.2f (%d orders, %d salespersons, top: %s)\n",
			region, stats.TotalSales, stats.OrderCount, stats.Salespersons, stats.TopProduct)
	}
	
	fmt.Printf("📄 Text summary: %s\n", filename)
	return nil
}

// generateCSVSummary creates a CSV summary report
func (fp *FileProcessor) generateCSVSummary() error {
	filename := filepath.Join(fp.outputDir, "product_summary.csv")
	
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()
	
	writer := csv.NewWriter(file)
	defer writer.Flush()
	
	// Write header
	header := []string{"Product", "Total Sales", "Total Quantity", "Order Count", "Avg Sale"}
	if err := writer.Write(header); err != nil {
		return err
	}
	
	// Write product data
	for _, product := range fp.analysis.TopProducts {
		avgSale := product.TotalSales / float64(product.OrderCount)
		record := []string{
			product.Product,
			fmt.Sprintf("%.2f", product.TotalSales),
			strconv.Itoa(product.TotalQuantity),
			strconv.Itoa(product.OrderCount),
			fmt.Sprintf("%.2f", avgSale),
		}
		if err := writer.Write(record); err != nil {
			return err
		}
	}
	
	fmt.Printf("📄 CSV summary: %s\n", filename)
	return nil
}

// Main function
func main() {
	fmt.Println("=== FILE PROCESSING TOOL ===")
	fmt.Println("Sales Data Analyzer")
	fmt.Println()
	
	// Initialize processor
	processor := NewFileProcessor("sales_data.csv", "reports")
	
	// Create sample data if file doesn't exist
	if _, err := os.Stat(processor.inputFile); os.IsNotExist(err) {
		if err := processor.CreateSampleData(); err != nil {
			fmt.Printf("❌ Error creating sample data: %v\n", err)
			return
		}
	}
	
	// Process the file
	if err := processor.ReadCSVFile(); err != nil {
		fmt.Printf("❌ Error reading CSV file: %v\n", err)
		return
	}
	
	// Analyze the data
	if err := processor.AnalyzeData(); err != nil {
		fmt.Printf("❌ Error analyzing data: %v\n", err)
		return
	}
	
	// Generate reports
	if err := processor.GenerateReports(); err != nil {
		fmt.Printf("❌ Error generating reports: %v\n", err)
		return
	}
	
	// Display summary
	fmt.Println("\n=== ANALYSIS COMPLETE ===")
	fmt.Printf("📊 Processed %d sales records\n", processor.analysis.TotalRecords)
	fmt.Printf("💰 Total Revenue: $%.2f\n", processor.analysis.TotalRevenue)
	fmt.Printf("📈 Average Order: $%.2f\n", processor.analysis.AverageOrderValue)
	fmt.Printf("⏱️ Processing Time: %v\n", processor.analysis.ProcessingTime)
	fmt.Println()
	fmt.Println("📁 Reports generated in 'reports/' directory:")
	fmt.Println("  - analysis_report.json (detailed JSON)")
	fmt.Println("  - summary.txt (human-readable)")
	fmt.Println("  - product_summary.csv (CSV format)")
	fmt.Println()
	fmt.Println("File processing demonstration complete! 📊")
}
